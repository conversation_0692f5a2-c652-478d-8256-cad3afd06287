<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Codecs Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Codecs Documentation
      </h1>


<div class="top-level-extent" id="SEC_Top">

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Codec-Options" href="#Codec-Options">2 Codec Options</a></li>
  <li><a id="toc-Decoders" href="#Decoders">3 Decoders</a></li>
  <li><a id="toc-Video-Decoders" href="#Video-Decoders">4 Video Decoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-av1" href="#av1">4.1 av1</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options" href="#Options">4.1.1 Options</a></li>
    </ul></li>
    <li><a id="toc-hevc" href="#hevc">4.2 hevc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-1" href="#Options-1">4.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-rawvideo" href="#rawvideo">4.3 rawvideo</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-2" href="#Options-2">4.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libdav1d" href="#libdav1d">4.4 libdav1d</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-3" href="#Options-3">4.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libdavs2" href="#libdavs2">4.5 libdavs2</a></li>
    <li><a id="toc-libuavs3d" href="#libuavs3d">4.6 libuavs3d</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-4" href="#Options-4">4.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxevd" href="#libxevd">4.7 libxevd</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-5" href="#Options-5">4.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-QSV-Decoders" href="#QSV-Decoders">4.8 QSV Decoders</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Common-Options" href="#Common-Options">4.8.1 Common Options</a></li>
      <li><a id="toc-HEVC-Options" href="#HEVC-Options">4.8.2 HEVC Options</a></li>
    </ul></li>
    <li><a id="toc-v210" href="#v210">4.9 v210</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-6" href="#Options-6">4.9.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Audio-Decoders" href="#Audio-Decoders">5 Audio Decoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-ac3" href="#ac3">5.1 ac3</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-AC_002d3-Decoder-Options" href="#AC_002d3-Decoder-Options">5.1.1 AC-3 Decoder Options</a></li>
    </ul></li>
    <li><a id="toc-flac-1" href="#flac-1">5.2 flac</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-FLAC-Decoder-options" href="#FLAC-Decoder-options">5.2.1 FLAC Decoder options</a></li>
    </ul></li>
    <li><a id="toc-ffwavesynth" href="#ffwavesynth">5.3 ffwavesynth</a></li>
    <li><a id="toc-libcelt" href="#libcelt">5.4 libcelt</a></li>
    <li><a id="toc-libgsm" href="#libgsm">5.5 libgsm</a></li>
    <li><a id="toc-libilbc" href="#libilbc">5.6 libilbc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-7" href="#Options-7">5.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopencore_002damrnb" href="#libopencore_002damrnb">5.7 libopencore-amrnb</a></li>
    <li><a id="toc-libopencore_002damrwb" href="#libopencore_002damrwb">5.8 libopencore-amrwb</a></li>
    <li><a id="toc-libopus" href="#libopus">5.9 libopus</a></li>
  </ul></li>
  <li><a id="toc-Subtitles-Decoders" href="#Subtitles-Decoders">6 Subtitles Decoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-libaribb24" href="#libaribb24">6.1 libaribb24</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-libaribb24-Decoder-Options" href="#libaribb24-Decoder-Options">6.1.1 libaribb24 Decoder Options</a></li>
    </ul></li>
    <li><a id="toc-libaribcaption" href="#libaribcaption">6.2 libaribcaption</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-libaribcaption-Decoder-Options" href="#libaribcaption-Decoder-Options">6.2.1 libaribcaption Decoder Options</a></li>
      <li><a id="toc-libaribcaption-decoder-usage-examples" href="#libaribcaption-decoder-usage-examples">6.2.2 libaribcaption decoder usage examples</a></li>
    </ul></li>
    <li><a id="toc-dvbsub" href="#dvbsub">6.3 dvbsub</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-8" href="#Options-8">6.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-dvdsub" href="#dvdsub">6.4 dvdsub</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-9" href="#Options-9">6.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libzvbi_002dteletext" href="#libzvbi_002dteletext">6.5 libzvbi-teletext</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-10" href="#Options-10">6.5.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Encoders" href="#Encoders">7 Encoders</a></li>
  <li><a id="toc-Audio-Encoders" href="#Audio-Encoders">8 Audio Encoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-aac" href="#aac">8.1 aac</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-11" href="#Options-11">8.1.1 Options</a></li>
    </ul></li>
    <li><a id="toc-ac3-and-ac3_005ffixed" href="#ac3-and-ac3_005ffixed">8.2 ac3 and ac3_fixed</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-AC_002d3-Metadata" href="#AC_002d3-Metadata">8.2.1 AC-3 Metadata</a>
      <ul class="toc-numbered-mark">
        <li><a id="toc-Metadata-Control-Options" href="#Metadata-Control-Options">8.2.1.1 Metadata Control Options</a></li>
        <li><a id="toc-Downmix-Levels" href="#Downmix-Levels">8.2.1.2 Downmix Levels</a></li>
        <li><a id="toc-Audio-Production-Information" href="#Audio-Production-Information">8.2.1.3 Audio Production Information</a></li>
        <li><a id="toc-Other-Metadata-Options" href="#Other-Metadata-Options">8.2.1.4 Other Metadata Options</a></li>
      </ul></li>
      <li><a id="toc-Extended-Bitstream-Information" href="#Extended-Bitstream-Information">8.2.2 Extended Bitstream Information</a>
      <ul class="toc-numbered-mark">
        <li><a id="toc-Extended-Bitstream-Information-_002d-Part-1" href="#Extended-Bitstream-Information-_002d-Part-1">8.2.2.1 Extended Bitstream Information - Part 1</a></li>
        <li><a id="toc-Extended-Bitstream-Information-_002d-Part-2" href="#Extended-Bitstream-Information-_002d-Part-2">8.2.2.2 Extended Bitstream Information - Part 2</a></li>
      </ul></li>
      <li><a id="toc-Other-AC_002d3-Encoding-Options" href="#Other-AC_002d3-Encoding-Options">8.2.3 Other AC-3 Encoding Options</a></li>
      <li><a id="toc-Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options" href="#Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options">8.2.4 Floating-Point-Only AC-3 Encoding Options</a></li>
    </ul></li>
    <li><a id="toc-flac-2" href="#flac-2">8.3 flac</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-12" href="#Options-12">8.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-opus" href="#opus">8.4 opus</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-13" href="#Options-13">8.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libfdk_005faac" href="#libfdk_005faac">8.5 libfdk_aac</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-14" href="#Options-14">8.5.1 Options</a></li>
      <li><a id="toc-Examples" href="#Examples">8.5.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-liblc3" href="#liblc3">8.6 liblc3</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-15" href="#Options-15">8.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libmp3lame-1" href="#libmp3lame-1">8.7 libmp3lame</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-16" href="#Options-16">8.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopencore_002damrnb-1" href="#libopencore_002damrnb-1">8.8 libopencore-amrnb</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-17" href="#Options-17">8.8.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopus-1" href="#libopus-1">8.9 libopus</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Option-Mapping" href="#Option-Mapping">8.9.1 Option Mapping</a></li>
    </ul></li>
    <li><a id="toc-libshine-1" href="#libshine-1">8.10 libshine</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-18" href="#Options-18">8.10.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libtwolame" href="#libtwolame">8.11 libtwolame</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-19" href="#Options-19">8.11.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libvo_002damrwbenc" href="#libvo_002damrwbenc">8.12 libvo-amrwbenc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-20" href="#Options-20">8.12.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libvorbis" href="#libvorbis">8.13 libvorbis</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-21" href="#Options-21">8.13.1 Options</a></li>
    </ul></li>
    <li><a id="toc-mjpeg" href="#mjpeg">8.14 mjpeg</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-22" href="#Options-22">8.14.1 Options</a></li>
    </ul></li>
    <li><a id="toc-wavpack" href="#wavpack">8.15 wavpack</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-23" href="#Options-23">8.15.1 Options</a>
      <ul class="toc-numbered-mark">
        <li><a id="toc-Shared-options" href="#Shared-options">8.15.1.1 Shared options</a></li>
        <li><a id="toc-Private-options" href="#Private-options">8.15.1.2 Private options</a></li>
      </ul></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Video-Encoders" href="#Video-Encoders">9 Video Encoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-a64_005fmulti_002c-a64_005fmulti5" href="#a64_005fmulti_002c-a64_005fmulti5">9.1 a64_multi, a64_multi5</a></li>
    <li><a id="toc-Cinepak" href="#Cinepak">9.2 Cinepak</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-24" href="#Options-24">9.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-GIF" href="#GIF">9.3 GIF</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-25" href="#Options-25">9.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-Hap" href="#Hap">9.4 Hap</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-26" href="#Options-26">9.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-jpeg2000" href="#jpeg2000">9.5 jpeg2000</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-27" href="#Options-27">9.5.1 Options</a></li>
    </ul></li>
    <li><a id="toc-librav1e" href="#librav1e">9.6 librav1e</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-28" href="#Options-28">9.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libaom_002dav1" href="#libaom_002dav1">9.7 libaom-av1</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-29" href="#Options-29">9.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libsvtav1" href="#libsvtav1">9.8 libsvtav1</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-30" href="#Options-30">9.8.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libjxl" href="#libjxl">9.9 libjxl</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-31" href="#Options-31">9.9.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libkvazaar" href="#libkvazaar">9.10 libkvazaar</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-32" href="#Options-32">9.10.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopenh264" href="#libopenh264">9.11 libopenh264</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-33" href="#Options-33">9.11.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libtheora" href="#libtheora">9.12 libtheora</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-34" href="#Options-34">9.12.1 Options</a></li>
      <li><a id="toc-Examples-1" href="#Examples-1">9.12.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-libvpx" href="#libvpx">9.13 libvpx</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-35" href="#Options-35">9.13.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libvvenc" href="#libvvenc">9.14 libvvenc</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Supported-Pixel-Formats" href="#Supported-Pixel-Formats">9.14.1 Supported Pixel Formats</a></li>
      <li><a id="toc-Options-36" href="#Options-36">9.14.2 Options</a></li>
    </ul></li>
    <li><a id="toc-libwebp" href="#libwebp">9.15 libwebp</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Pixel-Format" href="#Pixel-Format">9.15.1 Pixel Format</a></li>
      <li><a id="toc-Options-37" href="#Options-37">9.15.2 Options</a></li>
    </ul></li>
    <li><a id="toc-libx264_002c-libx264rgb" href="#libx264_002c-libx264rgb">9.16 libx264, libx264rgb</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Supported-Pixel-Formats-1" href="#Supported-Pixel-Formats-1">9.16.1 Supported Pixel Formats</a></li>
      <li><a id="toc-Options-38" href="#Options-38">9.16.2 Options</a></li>
    </ul></li>
    <li><a id="toc-libx265" href="#libx265">9.17 libx265</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-39" href="#Options-39">9.17.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxavs2" href="#libxavs2">9.18 libxavs2</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-40" href="#Options-40">9.18.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxeve" href="#libxeve">9.19 libxeve</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-41" href="#Options-41">9.19.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxvid" href="#libxvid">9.20 libxvid</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-42" href="#Options-42">9.20.1 Options</a></li>
    </ul></li>
    <li><a id="toc-MediaFoundation" href="#MediaFoundation">9.21 MediaFoundation</a></li>
    <li><a id="toc-Microsoft-RLE" href="#Microsoft-RLE">9.22 Microsoft RLE</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-43" href="#Options-43">9.22.1 Options</a></li>
    </ul></li>
    <li><a id="toc-mpeg2" href="#mpeg2">9.23 mpeg2</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-44" href="#Options-44">9.23.1 Options</a></li>
    </ul></li>
    <li><a id="toc-png" href="#png">9.24 png</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Private-options-1" href="#Private-options-1">9.24.1 Private options</a></li>
    </ul></li>
    <li><a id="toc-ProRes" href="#ProRes">9.25 ProRes</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Private-Options-for-prores_002dks" href="#Private-Options-for-prores_002dks">9.25.1 Private Options for prores-ks</a></li>
      <li><a id="toc-Speed-considerations" href="#Speed-considerations">9.25.2 Speed considerations</a></li>
    </ul></li>
    <li><a id="toc-QSV-Encoders" href="#QSV-Encoders">9.26 QSV Encoders</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Ratecontrol-Method" href="#Ratecontrol-Method">9.26.1 Ratecontrol Method</a></li>
      <li><a id="toc-Global-Options-_002d_003e-MSDK-Options" href="#Global-Options-_002d_003e-MSDK-Options">9.26.2 Global Options -&gt; MSDK Options</a></li>
      <li><a id="toc-Common-Options-1" href="#Common-Options-1">9.26.3 Common Options</a></li>
      <li><a id="toc-Runtime-Options" href="#Runtime-Options">9.26.4 Runtime Options</a></li>
      <li><a id="toc-H264-options" href="#H264-options">9.26.5 H264 options</a></li>
      <li><a id="toc-HEVC-Options-1" href="#HEVC-Options-1">9.26.6 HEVC Options</a></li>
      <li><a id="toc-MPEG2-Options" href="#MPEG2-Options">9.26.7 MPEG2 Options</a></li>
      <li><a id="toc-VP9-Options" href="#VP9-Options">9.26.8 VP9 Options</a></li>
      <li><a id="toc-AV1-Options" href="#AV1-Options">9.26.9 AV1 Options</a></li>
    </ul></li>
    <li><a id="toc-snow" href="#snow">9.27 snow</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-45" href="#Options-45">9.27.1 Options</a></li>
    </ul></li>
    <li><a id="toc-VAAPI-encoders" href="#VAAPI-encoders">9.28 VAAPI encoders</a></li>
    <li><a id="toc-vbn" href="#vbn">9.29 vbn</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-46" href="#Options-46">9.29.1 Options</a></li>
    </ul></li>
    <li><a id="toc-vc2" href="#vc2">9.30 vc2</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-47" href="#Options-47">9.30.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Subtitles-Encoders" href="#Subtitles-Encoders">10 Subtitles Encoders</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-dvdsub-1" href="#dvdsub-1">10.1 dvdsub</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Options-48" href="#Options-48">10.1.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-See-Also" href="#See-Also">11 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">12 Authors</a></li>
</ul>
</div>
</div>

<ul class="mini-toc">
<li><a href="#Description" accesskey="1">Description</a></li>
<li><a href="#Codec-Options" accesskey="2">Codec Options</a></li>
<li><a href="#Decoders" accesskey="3">Decoders</a></li>
<li><a href="#Video-Decoders" accesskey="4">Video Decoders</a></li>
<li><a href="#Audio-Decoders" accesskey="5">Audio Decoders</a></li>
<li><a href="#Subtitles-Decoders" accesskey="6">Subtitles Decoders</a></li>
<li><a href="#Encoders" accesskey="7">Encoders</a></li>
<li><a href="#Audio-Encoders" accesskey="8">Audio Encoders</a></li>
<li><a href="#Video-Encoders" accesskey="9">Video Encoders</a></li>
<li><a href="#Subtitles-Encoders">Subtitles Encoders</a></li>
<li><a href="#See-Also">See Also</a></li>
<li><a href="#Authors">Authors</a></li>
</ul>
<div class="chapter-level-extent" id="Description">
<h2 class="chapter"><span>1 Description<a class="copiable-link" href="#Description"> &para;</a></span></h2>

<p>This document describes the codecs (decoders and encoders) provided by
the libavcodec library.
</p>

<a class="anchor" id="codec_002doptions"></a></div>
<div class="chapter-level-extent" id="Codec-Options">
<h2 class="chapter"><span>2 Codec Options<a class="copiable-link" href="#Codec-Options"> &para;</a></span></h2>

<p>libavcodec provides some generic global options, which can be set on
all the encoders and decoders. In addition, each codec may support
so-called private options, which are specific for a given codec.
</p>
<p>Sometimes, a global option may only affect a specific kind of codec,
and may be nonsensical or ignored by another, so you need to be aware
of the meaning of the specified options. Also some options are
meant only for decoding or encoding.
</p>
<p>Options may be set by specifying -<var class="var">option</var> <var class="var">value</var> in the
FFmpeg tools, or by setting the value explicitly in the
<code class="code">AVCodecContext</code> options or using the <samp class="file">libavutil/opt.h</samp> API
for programmatic use.
</p>
<p>The list of supported options follow:
</p>
<dl class="table">
<dt><samp class="option">b <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd><p>Set bitrate in bits/s. Default value is 200K.
</p>
</dd>
<dt><samp class="option">ab <var class="var">integer</var> (<em class="emph">encoding,audio</em>)</samp></dt>
<dd><p>Set audio bitrate (in bits/s). Default value is 128K.
</p>
</dd>
<dt><samp class="option">bt <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set video bitrate tolerance (in bits/s). In 1-pass mode, bitrate
tolerance specifies how far ratecontrol is willing to deviate from the
target average bitrate value. This is not related to min/max
bitrate. Lowering tolerance too much has an adverse effect on quality.
</p>
</dd>
<dt><samp class="option">flags <var class="var">flags</var> (<em class="emph">decoding/encoding,audio,video,subtitles</em>)</samp></dt>
<dd><p>Set generic flags.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">mv4</samp>&rsquo;</dt>
<dd><p>Use four motion vector by macroblock (mpeg4).
</p></dd>
<dt>&lsquo;<samp class="samp">qpel</samp>&rsquo;</dt>
<dd><p>Use 1/4 pel motion compensation.
</p></dd>
<dt>&lsquo;<samp class="samp">loop</samp>&rsquo;</dt>
<dd><p>Use loop filter.
</p></dd>
<dt>&lsquo;<samp class="samp">qscale</samp>&rsquo;</dt>
<dd><p>Use fixed qscale.
</p></dd>
<dt>&lsquo;<samp class="samp">pass1</samp>&rsquo;</dt>
<dd><p>Use internal 2pass ratecontrol in first pass mode.
</p></dd>
<dt>&lsquo;<samp class="samp">pass2</samp>&rsquo;</dt>
<dd><p>Use internal 2pass ratecontrol in second pass mode.
</p></dd>
<dt>&lsquo;<samp class="samp">gray</samp>&rsquo;</dt>
<dd><p>Only decode/encode grayscale.
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>Set error[?] variables during encoding.
</p></dd>
<dt>&lsquo;<samp class="samp">truncated</samp>&rsquo;</dt>
<dd><p>Input bitstream might be randomly truncated.
</p></dd>
<dt>&lsquo;<samp class="samp">drop_changed</samp>&rsquo;</dt>
<dd><p>Don&rsquo;t output frames whose parameters differ from first decoded frame in stream.
Error AVERROR_INPUT_CHANGED is returned when a frame is dropped.
</p>
</dd>
<dt>&lsquo;<samp class="samp">ildct</samp>&rsquo;</dt>
<dd><p>Use interlaced DCT.
</p></dd>
<dt>&lsquo;<samp class="samp">low_delay</samp>&rsquo;</dt>
<dd><p>Force low delay.
</p></dd>
<dt>&lsquo;<samp class="samp">global_header</samp>&rsquo;</dt>
<dd><p>Place global headers in extradata instead of every keyframe.
</p></dd>
<dt>&lsquo;<samp class="samp">bitexact</samp>&rsquo;</dt>
<dd><p>Only write platform-, build- and time-independent data. (except (I)DCT).
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
</p></dd>
<dt>&lsquo;<samp class="samp">aic</samp>&rsquo;</dt>
<dd><p>Apply H263 advanced intra coding / mpeg4 ac prediction.
</p></dd>
<dt>&lsquo;<samp class="samp">ilme</samp>&rsquo;</dt>
<dd><p>Apply interlaced motion estimation.
</p></dd>
<dt>&lsquo;<samp class="samp">cgop</samp>&rsquo;</dt>
<dd><p>Use closed gop.
</p></dd>
<dt>&lsquo;<samp class="samp">output_corrupt</samp>&rsquo;</dt>
<dd><p>Output even potentially corrupted frames.
</p></dd>
</dl>

</dd>
<dt><samp class="option">time_base <var class="var">rational number</var></samp></dt>
<dd><p>Set codec time base.
</p>
<p>It is the fundamental unit of time (in seconds) in terms of which
frame timestamps are represented. For fixed-fps content, timebase
should be <code class="code">1 / frame_rate</code> and timestamp increments should be
identically 1.
</p>
</dd>
<dt><samp class="option">g <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set the group of picture (GOP) size. Default value is 12.
</p>
</dd>
<dt><samp class="option">ar <var class="var">integer</var> (<em class="emph">decoding/encoding,audio</em>)</samp></dt>
<dd><p>Set audio sampling rate (in Hz).
</p>
</dd>
<dt><samp class="option">ac <var class="var">integer</var> (<em class="emph">decoding/encoding,audio</em>)</samp></dt>
<dd><p>Set number of audio channels.
</p>
</dd>
<dt><samp class="option">cutoff <var class="var">integer</var> (<em class="emph">encoding,audio</em>)</samp></dt>
<dd><p>Set cutoff bandwidth. (Supported only by selected encoders, see
their respective documentation sections.)
</p>
</dd>
<dt><samp class="option">frame_size <var class="var">integer</var> (<em class="emph">encoding,audio</em>)</samp></dt>
<dd><p>Set audio frame size.
</p>
<p>Each submitted frame except the last must contain exactly frame_size
samples per channel. May be 0 when the codec has
CODEC_CAP_VARIABLE_FRAME_SIZE set, in that case the frame size is not
restricted. It is set by some decoders to indicate constant frame
size.
</p>
</dd>
<dt><samp class="option">frame_number <var class="var">integer</var></samp></dt>
<dd><p>Set the frame number.
</p>
</dd>
<dt><samp class="option">delay <var class="var">integer</var></samp></dt>
<dt><samp class="option">qcomp <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set video quantizer scale compression (VBR). It is used as a constant
in the ratecontrol equation. Recommended range for default rc_eq:
0.0-1.0.
</p>
</dd>
<dt><samp class="option">qblur <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set video quantizer scale blur (VBR).
</p>
</dd>
<dt><samp class="option">qmin <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set min video quantizer scale (VBR). Must be included between -1 and
69, default value is 2.
</p>
</dd>
<dt><samp class="option">qmax <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set max video quantizer scale (VBR). Must be included between -1 and
1024, default value is 31.
</p>
</dd>
<dt><samp class="option">qdiff <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set max difference between the quantizer scale (VBR).
</p>
</dd>
<dt><samp class="option">bf <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set max number of B frames between non-B-frames.
</p>
<p>Must be an integer between -1 and 16. 0 means that B-frames are
disabled. If a value of -1 is used, it will choose an automatic value
depending on the encoder.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">b_qfactor <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set qp factor between P and B frames.
</p>
</dd>
<dt><samp class="option">codec_tag <var class="var">integer</var></samp></dt>
<dt><samp class="option">bug <var class="var">flags</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Workaround not auto detected encoder bugs.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">autodetect</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">xvid_ilace</samp>&rsquo;</dt>
<dd><p>Xvid interlacing bug (autodetected if fourcc==XVIX)
</p></dd>
<dt>&lsquo;<samp class="samp">ump4</samp>&rsquo;</dt>
<dd><p>(autodetected if fourcc==UMP4)
</p></dd>
<dt>&lsquo;<samp class="samp">no_padding</samp>&rsquo;</dt>
<dd><p>padding bug (autodetected)
</p></dd>
<dt>&lsquo;<samp class="samp">amv</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">qpel_chroma</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">std_qpel</samp>&rsquo;</dt>
<dd><p>old standard qpel (autodetected per fourcc/version)
</p></dd>
<dt>&lsquo;<samp class="samp">qpel_chroma2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">direct_blocksize</samp>&rsquo;</dt>
<dd><p>direct-qpel-blocksize bug (autodetected per fourcc/version)
</p></dd>
<dt>&lsquo;<samp class="samp">edge</samp>&rsquo;</dt>
<dd><p>edge padding bug (autodetected per fourcc/version)
</p></dd>
<dt>&lsquo;<samp class="samp">hpel_chroma</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">dc_clip</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">ms</samp>&rsquo;</dt>
<dd><p>Workaround various bugs in microsoft broken decoders.
</p></dd>
<dt>&lsquo;<samp class="samp">trunc</samp>&rsquo;</dt>
<dd><p>trancated frames
</p></dd>
</dl>

</dd>
<dt><samp class="option">strict <var class="var">integer</var> (<em class="emph">decoding/encoding,audio,video</em>)</samp></dt>
<dd><p>Specify how strictly to follow the standards.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">very</samp>&rsquo;</dt>
<dd><p>strictly conform to an older more strict version of the spec or reference software
</p></dd>
<dt>&lsquo;<samp class="samp">strict</samp>&rsquo;</dt>
<dd><p>strictly conform to all the things in the spec no matter what consequences
</p></dd>
<dt>&lsquo;<samp class="samp">normal</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">unofficial</samp>&rsquo;</dt>
<dd><p>allow unofficial extensions
</p></dd>
<dt>&lsquo;<samp class="samp">experimental</samp>&rsquo;</dt>
<dd><p>allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
</p></dd>
</dl>

</dd>
<dt><samp class="option">b_qoffset <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set QP offset between P and B frames.
</p>
</dd>
<dt><samp class="option">err_detect <var class="var">flags</var> (<em class="emph">decoding,audio,video</em>)</samp></dt>
<dd><p>Set error detection flags.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">crccheck</samp>&rsquo;</dt>
<dd><p>verify embedded CRCs
</p></dd>
<dt>&lsquo;<samp class="samp">bitstream</samp>&rsquo;</dt>
<dd><p>detect bitstream specification deviations
</p></dd>
<dt>&lsquo;<samp class="samp">buffer</samp>&rsquo;</dt>
<dd><p>detect improper bitstream length
</p></dd>
<dt>&lsquo;<samp class="samp">explode</samp>&rsquo;</dt>
<dd><p>abort decoding on minor error detection
</p></dd>
<dt>&lsquo;<samp class="samp">ignore_err</samp>&rsquo;</dt>
<dd><p>ignore decoding errors, and continue decoding.
This is useful if you want to analyze the content of a video and thus want
everything to be decoded no matter what. This option will not result in a video
that is pleasing to watch in case of errors.
</p></dd>
<dt>&lsquo;<samp class="samp">careful</samp>&rsquo;</dt>
<dd><p>consider things that violate the spec and have not been seen in the wild as errors
</p></dd>
<dt>&lsquo;<samp class="samp">compliant</samp>&rsquo;</dt>
<dd><p>consider all spec non compliancies as errors
</p></dd>
<dt>&lsquo;<samp class="samp">aggressive</samp>&rsquo;</dt>
<dd><p>consider things that a sane encoder should not do as an error
</p></dd>
</dl>

</dd>
<dt><samp class="option">has_b_frames <var class="var">integer</var></samp></dt>
<dt><samp class="option">block_align <var class="var">integer</var></samp></dt>
<dt><samp class="option">rc_override_count <var class="var">integer</var></samp></dt>
<dt><samp class="option">maxrate <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd><p>Set max bitrate tolerance (in bits/s). Requires bufsize to be set.
</p>
</dd>
<dt><samp class="option">minrate <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd><p>Set min bitrate tolerance (in bits/s). Most useful in setting up a CBR
encode. It is of little use elsewise.
</p>
</dd>
<dt><samp class="option">bufsize <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd><p>Set ratecontrol buffer size (in bits).
</p>
</dd>
<dt><samp class="option">i_qfactor <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set QP factor between P and I frames.
</p>
</dd>
<dt><samp class="option">i_qoffset <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set QP offset between P and I frames.
</p>
</dd>
<dt><samp class="option">dct <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set DCT algorithm.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>autoselect a good one (default)
</p></dd>
<dt>&lsquo;<samp class="samp">fastint</samp>&rsquo;</dt>
<dd><p>fast integer
</p></dd>
<dt>&lsquo;<samp class="samp">int</samp>&rsquo;</dt>
<dd><p>accurate integer
</p></dd>
<dt>&lsquo;<samp class="samp">mmx</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">altivec</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">faan</samp>&rsquo;</dt>
<dd><p>floating point AAN DCT
</p></dd>
</dl>

</dd>
<dt><samp class="option">lumi_mask <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Compress bright areas stronger than medium ones.
</p>
</dd>
<dt><samp class="option">tcplx_mask <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set temporal complexity masking.
</p>
</dd>
<dt><samp class="option">scplx_mask <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set spatial complexity masking.
</p>
</dd>
<dt><samp class="option">p_mask <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set inter masking.
</p>
</dd>
<dt><samp class="option">dark_mask <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Compress dark areas stronger than medium ones.
</p>
</dd>
<dt><samp class="option">idct <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Select IDCT implementation.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">int</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simplemmx</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simpleauto</samp>&rsquo;</dt>
<dd><p>Automatically pick a IDCT compatible with the simple one
</p>
</dd>
<dt>&lsquo;<samp class="samp">arm</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">altivec</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sh4</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simplearm</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simplearmv5te</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simplearmv6</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simpleneon</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">xvid</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">faani</samp>&rsquo;</dt>
<dd><p>floating point AAN IDCT
</p></dd>
</dl>

</dd>
<dt><samp class="option">slice_count <var class="var">integer</var></samp></dt>
<dt><samp class="option">ec <var class="var">flags</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Set error concealment strategy.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">guess_mvs</samp>&rsquo;</dt>
<dd><p>iterative motion vector (MV) search (slow)
</p></dd>
<dt>&lsquo;<samp class="samp">deblock</samp>&rsquo;</dt>
<dd><p>use strong deblock filter for damaged MBs
</p></dd>
<dt>&lsquo;<samp class="samp">favor_inter</samp>&rsquo;</dt>
<dd><p>favor predicting from the previous frame instead of the current
</p></dd>
</dl>

</dd>
<dt><samp class="option">bits_per_coded_sample <var class="var">integer</var></samp></dt>
<dt><samp class="option">aspect <var class="var">rational number</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set sample aspect ratio.
</p>
</dd>
<dt><samp class="option">sar <var class="var">rational number</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set sample aspect ratio. Alias to <var class="var">aspect</var>.
</p>
</dd>
<dt><samp class="option">debug <var class="var">flags</var> (<em class="emph">decoding/encoding,audio,video,subtitles</em>)</samp></dt>
<dd><p>Print specific debug info.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">pict</samp>&rsquo;</dt>
<dd><p>picture info
</p></dd>
<dt>&lsquo;<samp class="samp">rc</samp>&rsquo;</dt>
<dd><p>rate control
</p></dd>
<dt>&lsquo;<samp class="samp">bitstream</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">mb_type</samp>&rsquo;</dt>
<dd><p>macroblock (MB) type
</p></dd>
<dt>&lsquo;<samp class="samp">qp</samp>&rsquo;</dt>
<dd><p>per-block quantization parameter (QP)
</p></dd>
<dt>&lsquo;<samp class="samp">dct_coeff</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">green_metadata</samp>&rsquo;</dt>
<dd><p>display complexity metadata for the upcoming frame, GoP or for a given duration.
</p>
</dd>
<dt>&lsquo;<samp class="samp">skip</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">startcode</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">er</samp>&rsquo;</dt>
<dd><p>error recognition
</p></dd>
<dt>&lsquo;<samp class="samp">mmco</samp>&rsquo;</dt>
<dd><p>memory management control operations (H.264)
</p></dd>
<dt>&lsquo;<samp class="samp">bugs</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">buffers</samp>&rsquo;</dt>
<dd><p>picture buffer allocations
</p></dd>
<dt>&lsquo;<samp class="samp">thread_ops</samp>&rsquo;</dt>
<dd><p>threading operations
</p></dd>
<dt>&lsquo;<samp class="samp">nomc</samp>&rsquo;</dt>
<dd><p>skip motion compensation
</p></dd>
</dl>

</dd>
<dt><samp class="option">cmp <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set full pel me compare function.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dd><p>sum of squared errors
</p></dd>
<dt>&lsquo;<samp class="samp">satd</samp>&rsquo;</dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">dct</samp>&rsquo;</dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt>&lsquo;<samp class="samp">bit</samp>&rsquo;</dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt>&lsquo;<samp class="samp">zero</samp>&rsquo;</dt>
<dd><p>0
</p></dd>
<dt>&lsquo;<samp class="samp">vsad</samp>&rsquo;</dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">vsse</samp>&rsquo;</dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">nsse</samp>&rsquo;</dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt>&lsquo;<samp class="samp">w53</samp>&rsquo;</dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">w97</samp>&rsquo;</dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">dctmax</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">subcmp <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set sub pel me compare function.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dd><p>sum of squared errors
</p></dd>
<dt>&lsquo;<samp class="samp">satd</samp>&rsquo;</dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">dct</samp>&rsquo;</dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt>&lsquo;<samp class="samp">bit</samp>&rsquo;</dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt>&lsquo;<samp class="samp">zero</samp>&rsquo;</dt>
<dd><p>0
</p></dd>
<dt>&lsquo;<samp class="samp">vsad</samp>&rsquo;</dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">vsse</samp>&rsquo;</dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">nsse</samp>&rsquo;</dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt>&lsquo;<samp class="samp">w53</samp>&rsquo;</dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">w97</samp>&rsquo;</dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">dctmax</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">mbcmp <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set macroblock compare function.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dd><p>sum of squared errors
</p></dd>
<dt>&lsquo;<samp class="samp">satd</samp>&rsquo;</dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">dct</samp>&rsquo;</dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt>&lsquo;<samp class="samp">bit</samp>&rsquo;</dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt>&lsquo;<samp class="samp">zero</samp>&rsquo;</dt>
<dd><p>0
</p></dd>
<dt>&lsquo;<samp class="samp">vsad</samp>&rsquo;</dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">vsse</samp>&rsquo;</dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">nsse</samp>&rsquo;</dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt>&lsquo;<samp class="samp">w53</samp>&rsquo;</dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">w97</samp>&rsquo;</dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">dctmax</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">ildctcmp <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set interlaced dct compare function.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dd><p>sum of squared errors
</p></dd>
<dt>&lsquo;<samp class="samp">satd</samp>&rsquo;</dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">dct</samp>&rsquo;</dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt>&lsquo;<samp class="samp">bit</samp>&rsquo;</dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt>&lsquo;<samp class="samp">zero</samp>&rsquo;</dt>
<dd><p>0
</p></dd>
<dt>&lsquo;<samp class="samp">vsad</samp>&rsquo;</dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">vsse</samp>&rsquo;</dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">nsse</samp>&rsquo;</dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt>&lsquo;<samp class="samp">w53</samp>&rsquo;</dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">w97</samp>&rsquo;</dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">dctmax</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">dia_size <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set diamond type &amp; size for motion estimation.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">(1024, INT_MAX)</samp>&rsquo;</dt>
<dd><p>full motion estimation(slowest)
</p></dd>
<dt>&lsquo;<samp class="samp">(768, 1024]</samp>&rsquo;</dt>
<dd><p>umh motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">(512, 768]</samp>&rsquo;</dt>
<dd><p>hex motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">(256, 512]</samp>&rsquo;</dt>
<dd><p>l2s diamond motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">[2,256]</samp>&rsquo;</dt>
<dd><p>var diamond motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">(-1,  2)</samp>&rsquo;</dt>
<dd><p>small diamond motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">-1</samp>&rsquo;</dt>
<dd><p>funny diamond motion estimation
</p></dd>
<dt>&lsquo;<samp class="samp">(INT_MIN, -1)</samp>&rsquo;</dt>
<dd><p>sab diamond motion estimation
</p></dd>
</dl>

</dd>
<dt><samp class="option">last_pred <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set amount of motion predictors from the previous frame.
</p>
</dd>
<dt><samp class="option">precmp <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set pre motion estimation compare function.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dd><p>sum of squared errors
</p></dd>
<dt>&lsquo;<samp class="samp">satd</samp>&rsquo;</dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">dct</samp>&rsquo;</dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt>&lsquo;<samp class="samp">psnr</samp>&rsquo;</dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt>&lsquo;<samp class="samp">bit</samp>&rsquo;</dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt>&lsquo;<samp class="samp">zero</samp>&rsquo;</dt>
<dd><p>0
</p></dd>
<dt>&lsquo;<samp class="samp">vsad</samp>&rsquo;</dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">vsse</samp>&rsquo;</dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt>&lsquo;<samp class="samp">nsse</samp>&rsquo;</dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt>&lsquo;<samp class="samp">w53</samp>&rsquo;</dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">w97</samp>&rsquo;</dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt>&lsquo;<samp class="samp">dctmax</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">pre_dia_size <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set diamond type &amp; size for motion estimation pre-pass.
</p>
</dd>
<dt><samp class="option">subq <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set sub pel motion estimation quality.
</p>
</dd>
<dt><samp class="option">me_range <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set limit motion vectors range (1023 for DivX player).
</p>
</dd>
<dt><samp class="option">global_quality <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dt><samp class="option">slice_flags <var class="var">integer</var></samp></dt>
<dt><samp class="option">mbd <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set macroblock decision algorithm (high quality mode).
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
<dd><p>use mbcmp (default)
</p></dd>
<dt>&lsquo;<samp class="samp">bits</samp>&rsquo;</dt>
<dd><p>use fewest bits
</p></dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>use best rate distortion
</p></dd>
</dl>

</dd>
<dt><samp class="option">rc_init_occupancy <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set number of bits which should be loaded into the rc buffer before
decoding starts.
</p>
</dd>
<dt><samp class="option">flags2 <var class="var">flags</var> (<em class="emph">decoding/encoding,audio,video,subtitles</em>)</samp></dt>
<dd>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">fast</samp>&rsquo;</dt>
<dd><p>Allow non spec compliant speedup tricks.
</p></dd>
<dt>&lsquo;<samp class="samp">noout</samp>&rsquo;</dt>
<dd><p>Skip bitstream encoding.
</p></dd>
<dt>&lsquo;<samp class="samp">ignorecrop</samp>&rsquo;</dt>
<dd><p>Ignore cropping information from sps.
</p></dd>
<dt>&lsquo;<samp class="samp">local_header</samp>&rsquo;</dt>
<dd><p>Place global headers at every keyframe instead of in extradata.
</p></dd>
<dt>&lsquo;<samp class="samp">chunks</samp>&rsquo;</dt>
<dd><p>Frame data might be split into multiple chunks.
</p></dd>
<dt>&lsquo;<samp class="samp">showall</samp>&rsquo;</dt>
<dd><p>Show all frames before the first keyframe.
</p></dd>
<dt>&lsquo;<samp class="samp">export_mvs</samp>&rsquo;</dt>
<dd><p>Export motion vectors into frame side-data (see <code class="code">AV_FRAME_DATA_MOTION_VECTORS</code>)
for codecs that support it. See also <samp class="file">doc/examples/export_mvs.c</samp>.
</p></dd>
<dt>&lsquo;<samp class="samp">skip_manual</samp>&rsquo;</dt>
<dd><p>Do not skip samples and export skip information as frame side data.
</p></dd>
<dt>&lsquo;<samp class="samp">ass_ro_flush_noop</samp>&rsquo;</dt>
<dd><p>Do not reset ASS ReadOrder field on flush.
</p></dd>
<dt>&lsquo;<samp class="samp">icc_profiles</samp>&rsquo;</dt>
<dd><p>Generate/parse embedded ICC profiles from/to colorimetry tags.
</p></dd>
</dl>

</dd>
<dt><samp class="option">export_side_data <var class="var">flags</var> (<em class="emph">decoding/encoding,audio,video,subtitles</em>)</samp></dt>
<dd>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">mvs</samp>&rsquo;</dt>
<dd><p>Export motion vectors into frame side-data (see <code class="code">AV_FRAME_DATA_MOTION_VECTORS</code>)
for codecs that support it. See also <samp class="file">doc/examples/export_mvs.c</samp>.
</p></dd>
<dt>&lsquo;<samp class="samp">prft</samp>&rsquo;</dt>
<dd><p>Export encoder Producer Reference Time into packet side-data (see <code class="code">AV_PKT_DATA_PRFT</code>)
for codecs that support it.
</p></dd>
<dt>&lsquo;<samp class="samp">venc_params</samp>&rsquo;</dt>
<dd><p>Export video encoding parameters through frame side data (see <code class="code">AV_FRAME_DATA_VIDEO_ENC_PARAMS</code>)
for codecs that support it. At present, those are H.264 and VP9.
</p></dd>
<dt>&lsquo;<samp class="samp">film_grain</samp>&rsquo;</dt>
<dd><p>Export film grain parameters through frame side data (see <code class="code">AV_FRAME_DATA_FILM_GRAIN_PARAMS</code>).
Supported at present by AV1 decoders.
</p></dd>
</dl>

</dd>
<dt><samp class="option">threads <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Set the number of threads to be used, in case the selected codec
implementation supports multi-threading.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto, 0</samp>&rsquo;</dt>
<dd><p>automatically select the number of threads to set
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">auto</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">dc <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set intra_dc_precision.
</p>
</dd>
<dt><samp class="option">nssew <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set nsse weight.
</p>
</dd>
<dt><samp class="option">skip_top <var class="var">integer</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Set number of macroblock rows at the top which are skipped.
</p>
</dd>
<dt><samp class="option">skip_bottom <var class="var">integer</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Set number of macroblock rows at the bottom which are skipped.
</p>
</dd>
<dt><samp class="option">profile <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd>
<p>Set encoder codec profile. Default value is &lsquo;<samp class="samp">unknown</samp>&rsquo;. Encoder specific
profiles are documented in the relevant encoder documentation.
</p>
</dd>
<dt><samp class="option">level <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd>
<p>Set the encoder level. This level depends on the specific codec, and
might correspond to the profile level. It is set by default to
&lsquo;<samp class="samp">unknown</samp>&rsquo;.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">lowres <var class="var">integer</var> (<em class="emph">decoding,audio,video</em>)</samp></dt>
<dd><p>Decode at 1= 1/2, 2=1/4, 3=1/8 resolutions.
</p>
</dd>
<dt><samp class="option">mblmin <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set min macroblock lagrange factor (VBR).
</p>
</dd>
<dt><samp class="option">mblmax <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set max macroblock lagrange factor (VBR).
</p>
</dd>
<dt><samp class="option">skip_loop_filter <var class="var">integer</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dt><samp class="option">skip_idct        <var class="var">integer</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dt><samp class="option">skip_frame       <var class="var">integer</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd>
<p>Make decoder discard processing depending on the frame type selected
by the option value.
</p>
<p><samp class="option">skip_loop_filter</samp> skips frame loop filtering, <samp class="option">skip_idct</samp>
skips frame IDCT/dequantization, <samp class="option">skip_frame</samp> skips decoding.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dd><p>Discard no frame.
</p>
</dd>
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dd><p>Discard useless frames like 0-sized frames.
</p>
</dd>
<dt>&lsquo;<samp class="samp">noref</samp>&rsquo;</dt>
<dd><p>Discard all non-reference frames.
</p>
</dd>
<dt>&lsquo;<samp class="samp">bidir</samp>&rsquo;</dt>
<dd><p>Discard all bidirectional frames.
</p>
</dd>
<dt>&lsquo;<samp class="samp">nokey</samp>&rsquo;</dt>
<dd><p>Discard all frames excepts keyframes.
</p>
</dd>
<dt>&lsquo;<samp class="samp">nointra</samp>&rsquo;</dt>
<dd><p>Discard all frames except I frames.
</p>
</dd>
<dt>&lsquo;<samp class="samp">all</samp>&rsquo;</dt>
<dd><p>Discard all frames.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">default</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">bidir_refine <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Refine the two motion vectors used in bidirectional macroblocks.
</p>
</dd>
<dt><samp class="option">keyint_min <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set minimum interval between IDR-frames.
</p>
</dd>
<dt><samp class="option">refs <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Set reference frames to consider for motion compensation.
</p>
</dd>
<dt><samp class="option">trellis <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dd><p>Set rate-distortion optimal quantization.
</p>
</dd>
<dt><samp class="option">mv0_threshold <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dt><samp class="option">compression_level <var class="var">integer</var> (<em class="emph">encoding,audio,video</em>)</samp></dt>
<dt><samp class="option">bits_per_raw_sample <var class="var">integer</var></samp></dt>
<dt><samp class="option">channel_layout <var class="var">integer</var> (<em class="emph">decoding/encoding,audio</em>)</samp></dt>
<dd><p>See <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#channel-layout-syntax">the Channel Layout section in the ffmpeg-utils(1) manual</a>
for the required syntax.
</p>
</dd>
<dt><samp class="option">rc_max_vbv_use <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dt><samp class="option">rc_min_vbv_use <var class="var">float</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dt><samp class="option">color_primaries <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">bt709</samp>&rsquo;</dt>
<dd><p>BT.709
</p></dd>
<dt>&lsquo;<samp class="samp">bt470m</samp>&rsquo;</dt>
<dd><p>BT.470 M
</p></dd>
<dt>&lsquo;<samp class="samp">bt470bg</samp>&rsquo;</dt>
<dd><p>BT.470 BG
</p></dd>
<dt>&lsquo;<samp class="samp">smpte170m</samp>&rsquo;</dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt>&lsquo;<samp class="samp">smpte240m</samp>&rsquo;</dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt>&lsquo;<samp class="samp">film</samp>&rsquo;</dt>
<dd><p>Film
</p></dd>
<dt>&lsquo;<samp class="samp">bt2020</samp>&rsquo;</dt>
<dd><p>BT.2020
</p></dd>
<dt>&lsquo;<samp class="samp">smpte428</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">smpte428_1</samp>&rsquo;</dt>
<dd><p>SMPTE ST 428-1
</p></dd>
<dt>&lsquo;<samp class="samp">smpte431</samp>&rsquo;</dt>
<dd><p>SMPTE 431-2
</p></dd>
<dt>&lsquo;<samp class="samp">smpte432</samp>&rsquo;</dt>
<dd><p>SMPTE 432-1
</p></dd>
<dt>&lsquo;<samp class="samp">jedec-p22</samp>&rsquo;</dt>
<dd><p>JEDEC P22
</p></dd>
</dl>

</dd>
<dt><samp class="option">color_trc <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">bt709</samp>&rsquo;</dt>
<dd><p>BT.709
</p></dd>
<dt>&lsquo;<samp class="samp">gamma22</samp>&rsquo;</dt>
<dd><p>BT.470 M
</p></dd>
<dt>&lsquo;<samp class="samp">gamma28</samp>&rsquo;</dt>
<dd><p>BT.470 BG
</p></dd>
<dt>&lsquo;<samp class="samp">smpte170m</samp>&rsquo;</dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt>&lsquo;<samp class="samp">smpte240m</samp>&rsquo;</dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt>&lsquo;<samp class="samp">linear</samp>&rsquo;</dt>
<dd><p>Linear
</p></dd>
<dt>&lsquo;<samp class="samp">log</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">log100</samp>&rsquo;</dt>
<dd><p>Log
</p></dd>
<dt>&lsquo;<samp class="samp">log_sqrt</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">log316</samp>&rsquo;</dt>
<dd><p>Log square root
</p></dd>
<dt>&lsquo;<samp class="samp">iec61966_2_4</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">iec61966-2-4</samp>&rsquo;</dt>
<dd><p>IEC 61966-2-4
</p></dd>
<dt>&lsquo;<samp class="samp">bt1361</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bt1361e</samp>&rsquo;</dt>
<dd><p>BT.1361
</p></dd>
<dt>&lsquo;<samp class="samp">iec61966_2_1</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">iec61966-2-1</samp>&rsquo;</dt>
<dd><p>IEC 61966-2-1
</p></dd>
<dt>&lsquo;<samp class="samp">bt2020_10</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bt2020_10bit</samp>&rsquo;</dt>
<dd><p>BT.2020 - 10 bit
</p></dd>
<dt>&lsquo;<samp class="samp">bt2020_12</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bt2020_12bit</samp>&rsquo;</dt>
<dd><p>BT.2020 - 12 bit
</p></dd>
<dt>&lsquo;<samp class="samp">smpte2084</samp>&rsquo;</dt>
<dd><p>SMPTE ST 2084
</p></dd>
<dt>&lsquo;<samp class="samp">smpte428</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">smpte428_1</samp>&rsquo;</dt>
<dd><p>SMPTE ST 428-1
</p></dd>
<dt>&lsquo;<samp class="samp">arib-std-b67</samp>&rsquo;</dt>
<dd><p>ARIB STD-B67
</p></dd>
</dl>

</dd>
<dt><samp class="option">colorspace <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">rgb</samp>&rsquo;</dt>
<dd><p>RGB
</p></dd>
<dt>&lsquo;<samp class="samp">bt709</samp>&rsquo;</dt>
<dd><p>BT.709
</p></dd>
<dt>&lsquo;<samp class="samp">fcc</samp>&rsquo;</dt>
<dd><p>FCC
</p></dd>
<dt>&lsquo;<samp class="samp">bt470bg</samp>&rsquo;</dt>
<dd><p>BT.470 BG
</p></dd>
<dt>&lsquo;<samp class="samp">smpte170m</samp>&rsquo;</dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt>&lsquo;<samp class="samp">smpte240m</samp>&rsquo;</dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt>&lsquo;<samp class="samp">ycocg</samp>&rsquo;</dt>
<dd><p>YCOCG
</p></dd>
<dt>&lsquo;<samp class="samp">bt2020nc</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bt2020_ncl</samp>&rsquo;</dt>
<dd><p>BT.2020 NCL
</p></dd>
<dt>&lsquo;<samp class="samp">bt2020c</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bt2020_cl</samp>&rsquo;</dt>
<dd><p>BT.2020 CL
</p></dd>
<dt>&lsquo;<samp class="samp">smpte2085</samp>&rsquo;</dt>
<dd><p>SMPTE 2085
</p></dd>
<dt>&lsquo;<samp class="samp">chroma-derived-nc</samp>&rsquo;</dt>
<dd><p>Chroma-derived NCL
</p></dd>
<dt>&lsquo;<samp class="samp">chroma-derived-c</samp>&rsquo;</dt>
<dd><p>Chroma-derived CL
</p></dd>
<dt>&lsquo;<samp class="samp">ictcp</samp>&rsquo;</dt>
<dd><p>ICtCp
</p></dd>
</dl>

</dd>
<dt><samp class="option">color_range <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>If used as input parameter, it serves as a hint to the decoder, which
color_range the input has.
Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">tv</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">mpeg</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">limited</samp>&rsquo;</dt>
<dd><p>MPEG (219*2^(n-8))
</p></dd>
<dt>&lsquo;<samp class="samp">pc</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">jpeg</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">full</samp>&rsquo;</dt>
<dd><p>JPEG (2^n-1)
</p></dd>
</dl>

</dd>
<dt><samp class="option">chroma_sample_location <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">left</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">center</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">topleft</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">top</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bottomleft</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bottom</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">log_level_offset <var class="var">integer</var></samp></dt>
<dd><p>Set the log level offset.
</p>
</dd>
<dt><samp class="option">slices <var class="var">integer</var> (<em class="emph">encoding,video</em>)</samp></dt>
<dd><p>Number of slices, used in parallelized encoding.
</p>
</dd>
<dt><samp class="option">thread_type <var class="var">flags</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Select which multithreading methods to use.
</p>
<p>Use of &lsquo;<samp class="samp">frame</samp>&rsquo; will increase decoding delay by one frame per
thread, so clients which cannot provide future frames should not use
it.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">slice</samp>&rsquo;</dt>
<dd><p>Decode more than one part of a single frame at once.
</p>
<p>Multithreading using slices works only when the video was encoded with
slices.
</p>
</dd>
<dt>&lsquo;<samp class="samp">frame</samp>&rsquo;</dt>
<dd><p>Decode more than one frame at once.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">slice+frame</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">audio_service_type <var class="var">integer</var> (<em class="emph">encoding,audio</em>)</samp></dt>
<dd><p>Set audio service type.
</p>
<p>Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">ma</samp>&rsquo;</dt>
<dd><p>Main Audio Service
</p></dd>
<dt>&lsquo;<samp class="samp">ef</samp>&rsquo;</dt>
<dd><p>Effects
</p></dd>
<dt>&lsquo;<samp class="samp">vi</samp>&rsquo;</dt>
<dd><p>Visually Impaired
</p></dd>
<dt>&lsquo;<samp class="samp">hi</samp>&rsquo;</dt>
<dd><p>Hearing Impaired
</p></dd>
<dt>&lsquo;<samp class="samp">di</samp>&rsquo;</dt>
<dd><p>Dialogue
</p></dd>
<dt>&lsquo;<samp class="samp">co</samp>&rsquo;</dt>
<dd><p>Commentary
</p></dd>
<dt>&lsquo;<samp class="samp">em</samp>&rsquo;</dt>
<dd><p>Emergency
</p></dd>
<dt>&lsquo;<samp class="samp">vo</samp>&rsquo;</dt>
<dd><p>Voice Over
</p></dd>
<dt>&lsquo;<samp class="samp">ka</samp>&rsquo;</dt>
<dd><p>Karaoke
</p></dd>
</dl>

</dd>
<dt><samp class="option">request_sample_fmt <var class="var">sample_fmt</var> (<em class="emph">decoding,audio</em>)</samp></dt>
<dd><p>Set sample format audio decoders should prefer. Default value is
<code class="code">none</code>.
</p>
</dd>
<dt><samp class="option">pkt_timebase <var class="var">rational number</var></samp></dt>
<dt><samp class="option">sub_charenc <var class="var">encoding</var> (<em class="emph">decoding,subtitles</em>)</samp></dt>
<dd><p>Set the input subtitles character encoding.
</p>
</dd>
<dt><samp class="option">field_order  <var class="var">field_order</var> (<em class="emph">video</em>)</samp></dt>
<dd><p>Set/override the field order of the video.
Possible values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">progressive</samp>&rsquo;</dt>
<dd><p>Progressive video
</p></dd>
<dt>&lsquo;<samp class="samp">tt</samp>&rsquo;</dt>
<dd><p>Interlaced video, top field coded and displayed first
</p></dd>
<dt>&lsquo;<samp class="samp">bb</samp>&rsquo;</dt>
<dd><p>Interlaced video, bottom field coded and displayed first
</p></dd>
<dt>&lsquo;<samp class="samp">tb</samp>&rsquo;</dt>
<dd><p>Interlaced video, top coded first, bottom displayed first
</p></dd>
<dt>&lsquo;<samp class="samp">bt</samp>&rsquo;</dt>
<dd><p>Interlaced video, bottom coded first, top displayed first
</p></dd>
</dl>

</dd>
<dt><samp class="option">skip_alpha <var class="var">bool</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Set to 1 to disable processing alpha (transparency). This works like the
&lsquo;<samp class="samp">gray</samp>&rsquo; flag in the <samp class="option">flags</samp> option which skips chroma information
instead of alpha. Default is 0.
</p>
</dd>
<dt><samp class="option">codec_whitelist <var class="var">list</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>&quot;,&quot; separated list of allowed decoders. By default all are allowed.
</p>
</dd>
<dt><samp class="option">dump_separator <var class="var">string</var> (<em class="emph">input</em>)</samp></dt>
<dd><p>Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
</p><div class="example">
<pre class="example-preformatted">ffprobe -dump_separator &quot;
                          &quot;  -i ~/videos/matrixbench_mpeg2.mpg
</pre></div>

</dd>
<dt><samp class="option">max_pixels <var class="var">integer</var> (<em class="emph">decoding/encoding,video</em>)</samp></dt>
<dd><p>Maximum number of pixels per image. This value can be used to avoid out of
memory failures due to large images.
</p>
</dd>
<dt><samp class="option">apply_cropping <var class="var">bool</var> (<em class="emph">decoding,video</em>)</samp></dt>
<dd><p>Enable cropping if cropping parameters are multiples of the required
alignment for the left and top parameters. If the alignment is not met the
cropping will be partially applied to maintain alignment.
Default is 1 (enabled).
Note: The required alignment depends on if <code class="code">AV_CODEC_FLAG_UNALIGNED</code> is set and the
CPU. <code class="code">AV_CODEC_FLAG_UNALIGNED</code> cannot be changed from the command line. Also hardware
decoders will not apply left/top Cropping.
</p>

</dd>
</dl>


</div>
<div class="chapter-level-extent" id="Decoders">
<h2 class="chapter"><span>3 Decoders<a class="copiable-link" href="#Decoders"> &para;</a></span></h2>

<p>Decoders are configured elements in FFmpeg which allow the decoding of
multimedia streams.
</p>
<p>When you configure your FFmpeg build, all the supported native decoders
are enabled by default. Decoders requiring an external library must be enabled
manually via the corresponding <code class="code">--enable-lib</code> option. You can list all
available decoders using the configure option <code class="code">--list-decoders</code>.
</p>
<p>You can disable all the decoders with the configure option
<code class="code">--disable-decoders</code> and selectively enable / disable single decoders
with the options <code class="code">--enable-decoder=<var class="var">DECODER</var></code> /
<code class="code">--disable-decoder=<var class="var">DECODER</var></code>.
</p>
<p>The option <code class="code">-decoders</code> of the ff* tools will display the list of
enabled decoders.
</p>

</div>
<div class="chapter-level-extent" id="Video-Decoders">
<h2 class="chapter"><span>4 Video Decoders<a class="copiable-link" href="#Video-Decoders"> &para;</a></span></h2>

<p>A description of some of the currently available video decoders
follows.
</p>
<ul class="mini-toc">
<li><a href="#av1" accesskey="1">av1</a></li>
<li><a href="#hevc" accesskey="2">hevc</a></li>
<li><a href="#rawvideo" accesskey="3">rawvideo</a></li>
<li><a href="#libdav1d" accesskey="4">libdav1d</a></li>
<li><a href="#libdavs2" accesskey="5">libdavs2</a></li>
<li><a href="#libuavs3d" accesskey="6">libuavs3d</a></li>
<li><a href="#libxevd" accesskey="7">libxevd</a></li>
<li><a href="#QSV-Decoders" accesskey="8">QSV Decoders</a></li>
<li><a href="#v210" accesskey="9">v210</a></li>
</ul>
<div class="section-level-extent" id="av1">
<h3 class="section"><span>4.1 av1<a class="copiable-link" href="#av1"> &para;</a></span></h3>

<p>AOMedia Video 1 (AV1) decoder.
</p>
<ul class="mini-toc">
<li><a href="#Options" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options">
<h4 class="subsection"><span>4.1.1 Options<a class="copiable-link" href="#Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">operating_point</samp></dt>
<dd><p>Select an operating point of a scalable AV1 bitstream (0 - 31). Default is 0.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="hevc">
<h3 class="section"><span>4.2 hevc<a class="copiable-link" href="#hevc"> &para;</a></span></h3>
<p>HEVC (AKA ITU-T H.265 or ISO/IEC 23008-2) decoder.
</p>
<p>The decoder supports MV-HEVC multiview streams with at most two views. Views to
be output are selected by supplying a list of view IDs to the decoder (the
<samp class="option">view_ids</samp> option). This option may be set either statically before
decoder init, or from the <code class="code">get_format()</code> callback - useful for the case
when the view count or IDs change dynamically during decoding.
</p>
<p>Only the base layer is decoded by default.
</p>
<p>Note that if you are using the <code class="code">ffmpeg</code> CLI tool, you should be using view
specifiers as documented in its manual, rather than the options documented here.
</p>
<ul class="mini-toc">
<li><a href="#Options-1" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-1">
<h4 class="subsection"><span>4.2.1 Options<a class="copiable-link" href="#Options-1"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">view_ids (MV-HEVC)</samp></dt>
<dd><p>Specify a list of view IDs that should be output. This option can also be set to
a single &rsquo;-1&rsquo;, which will cause all views defined in the VPS to be decoded and
output.
</p>
</dd>
<dt><samp class="option">view_ids_available (MV-HEVC)</samp></dt>
<dd><p>This option may be read by the caller to retrieve an array of view IDs available
in the active VPS. The array is empty for single-layer video.
</p>
<p>The value of this option is guaranteed to be accurate when read from the
<code class="code">get_format()</code> callback. It may also be set at other times (e.g. after
opening the decoder), but the value is informational only and may be incorrect
(e.g. when the stream contains multiple distinct VPS NALUs).
</p>
</dd>
<dt><samp class="option">view_pos_available (MV-HEVC)</samp></dt>
<dd><p>This option may be read by the caller to retrieve an array of view positions
(left, right, or unspecified) available in the active VPS, as
<code class="code">AVStereo3DView</code> values. When the array is available, its elements apply to
the corresponding elements of <samp class="option">view_ids_available</samp>, i.e.
<code class="code">view_pos_available[i]</code> contains the position of view with ID
<code class="code">view_ids_available[i]</code>.
</p>
<p>Same validity restrictions as for <samp class="option">view_ids_available</samp> apply to
this option.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="rawvideo">
<h3 class="section"><span>4.3 rawvideo<a class="copiable-link" href="#rawvideo"> &para;</a></span></h3>

<p>Raw video decoder.
</p>
<p>This decoder decodes rawvideo streams.
</p>
<ul class="mini-toc">
<li><a href="#Options-2" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-2">
<h4 class="subsection"><span>4.3.1 Options<a class="copiable-link" href="#Options-2"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">top <var class="var">top_field_first</var></samp></dt>
<dd><p>Specify the assumed field type of the input video.
</p><dl class="table">
<dt><samp class="option">-1</samp></dt>
<dd><p>the video is assumed to be progressive (default)
</p></dd>
<dt><samp class="option">0</samp></dt>
<dd><p>bottom-field-first is assumed
</p></dd>
<dt><samp class="option">1</samp></dt>
<dd><p>top-field-first is assumed
</p></dd>
</dl>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libdav1d">
<h3 class="section"><span>4.4 libdav1d<a class="copiable-link" href="#libdav1d"> &para;</a></span></h3>

<p>dav1d AV1 decoder.
</p>
<p>libdav1d allows libavcodec to decode the AOMedia Video 1 (AV1) codec.
Requires the presence of the libdav1d headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-libdav1d</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-3" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-3">
<h4 class="subsection"><span>4.4.1 Options<a class="copiable-link" href="#Options-3"> &para;</a></span></h4>

<p>The following options are supported by the libdav1d wrapper.
</p>
<dl class="table">
<dt><samp class="option">framethreads</samp></dt>
<dd><p>Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d &gt;= 1.0 and will be removed in the future. Use the
option <code class="code">max_frame_delay</code> and the global option <code class="code">threads</code> instead.
</p>
</dd>
<dt><samp class="option">tilethreads</samp></dt>
<dd><p>Set amount of tile threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d &gt;= 1.0 and will be removed in the future. Use the
global option <code class="code">threads</code> instead.
</p>
</dd>
<dt><samp class="option">max_frame_delay</samp></dt>
<dd><p>Set max amount of frames the decoder may buffer internally. The default value is 0
(autodetect).
</p>
</dd>
<dt><samp class="option">filmgrain</samp></dt>
<dd><p>Apply film grain to the decoded video if present in the bitstream. Defaults to the
internal default of the library.
This option is deprecated and will be removed in the future. See the global option
<code class="code">export_side_data</code> to export Film Grain parameters instead of applying it.
</p>
</dd>
<dt><samp class="option">oppoint</samp></dt>
<dd><p>Select an operating point of a scalable AV1 bitstream (0 - 31). Defaults to the
internal default of the library.
</p>
</dd>
<dt><samp class="option">alllayers</samp></dt>
<dd><p>Output all spatial layers of a scalable AV1 bitstream. The default value is false.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libdavs2">
<h3 class="section"><span>4.5 libdavs2<a class="copiable-link" href="#libdavs2"> &para;</a></span></h3>

<p>AVS2-P2/IEEE1857.4 video decoder wrapper.
</p>
<p>This decoder allows libavcodec to decode AVS2 streams with davs2 library.
</p>

</div>
<div class="section-level-extent" id="libuavs3d">
<h3 class="section"><span>4.6 libuavs3d<a class="copiable-link" href="#libuavs3d"> &para;</a></span></h3>

<p>AVS3-P2/IEEE1857.10 video decoder.
</p>
<p>libuavs3d allows libavcodec to decode AVS3 streams.
Requires the presence of the libuavs3d headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-libuavs3d</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-4" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-4">
<h4 class="subsection"><span>4.6.1 Options<a class="copiable-link" href="#Options-4"> &para;</a></span></h4>

<p>The following option is supported by the libuavs3d wrapper.
</p>
<dl class="table">
<dt><samp class="option">frame_threads</samp></dt>
<dd><p>Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libxevd">
<h3 class="section"><span>4.7 libxevd<a class="copiable-link" href="#libxevd"> &para;</a></span></h3>

<p>eXtra-fast Essential Video Decoder (XEVD) MPEG-5 EVC decoder wrapper.
</p>
<p>This decoder requires the presence of the libxevd headers and library
during configuration. You need to explicitly configure the build with
<samp class="option">--enable-libxevd</samp>.
</p>
<p>The xevd project website is at <a class="url" href="https://github.com/mpeg5/xevd">https://github.com/mpeg5/xevd</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-5" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-5">
<h4 class="subsection"><span>4.7.1 Options<a class="copiable-link" href="#Options-5"> &para;</a></span></h4>

<p>The following options are supported by the libxevd wrapper.
The xevd-equivalent options or values are listed in parentheses for easy migration.
</p>
<p>To get a more accurate and extensive documentation of the libxevd options,
invoke the command  <code class="code">xevd_app --help</code> or consult the libxevd documentation.
</p>
<dl class="table">
<dt><samp class="option">threads (<em class="emph">threads</em>)</samp></dt>
<dd><p>Force to use a specific number of threads
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="QSV-Decoders">
<h3 class="section"><span>4.8 QSV Decoders<a class="copiable-link" href="#QSV-Decoders"> &para;</a></span></h3>

<p>The family of Intel QuickSync Video decoders (VC1, MPEG-2, H.264, HEVC,
JPEG/MJPEG, VP8, VP9, AV1, VVC).
</p>
<ul class="mini-toc">
<li><a href="#Common-Options" accesskey="1">Common Options</a></li>
<li><a href="#HEVC-Options" accesskey="2">HEVC Options</a></li>
</ul>
<div class="subsection-level-extent" id="Common-Options">
<h4 class="subsection"><span>4.8.1 Common Options<a class="copiable-link" href="#Common-Options"> &para;</a></span></h4>

<p>The following options are supported by all qsv decoders.
</p>
<dl class="table">
<dt><samp class="option"><var class="var">async_depth</var></samp></dt>
<dd><p>Internal parallelization depth, the higher the value the higher the latency.
</p>
</dd>
<dt><samp class="option"><var class="var">gpu_copy</var></samp></dt>
<dd><p>A GPU-accelerated copy between video and system memory
</p><dl class="table">
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">on</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">off</samp>&rsquo;</dt>
</dl>

</dd>
</dl>

</div>
<div class="subsection-level-extent" id="HEVC-Options">
<h4 class="subsection"><span>4.8.2 HEVC Options<a class="copiable-link" href="#HEVC-Options"> &para;</a></span></h4>
<p>Extra options for hevc_qsv.
</p>
<dl class="table">
<dt><samp class="option"><var class="var">load_plugin</var></samp></dt>
<dd><p>A user plugin to load in an internal session
</p><dl class="table">
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hevc_sw</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hevc_hw</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">load_plugins</var></samp></dt>
<dd><p>A :-separate list of hexadecimal plugin UIDs to load in an internal session
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="v210">
<h3 class="section"><span>4.9 v210<a class="copiable-link" href="#v210"> &para;</a></span></h3>

<p>Uncompressed 4:2:2 10-bit decoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-6" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-6">
<h4 class="subsection"><span>4.9.1 Options<a class="copiable-link" href="#Options-6"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">custom_stride</samp></dt>
<dd><p>Set the line size of the v210 data in bytes. The default value is 0
(autodetect). You can use the special -1 value for a strideless v210 as seen in
BOXX files.
</p>
</dd>
</dl>


</div>
</div>
</div>
<div class="chapter-level-extent" id="Audio-Decoders">
<h2 class="chapter"><span>5 Audio Decoders<a class="copiable-link" href="#Audio-Decoders"> &para;</a></span></h2>

<p>A description of some of the currently available audio decoders
follows.
</p>
<ul class="mini-toc">
<li><a href="#ac3" accesskey="1">ac3</a></li>
<li><a href="#flac-1" accesskey="2">flac</a></li>
<li><a href="#ffwavesynth" accesskey="3">ffwavesynth</a></li>
<li><a href="#libcelt" accesskey="4">libcelt</a></li>
<li><a href="#libgsm" accesskey="5">libgsm</a></li>
<li><a href="#libilbc" accesskey="6">libilbc</a></li>
<li><a href="#libopencore_002damrnb" accesskey="7">libopencore-amrnb</a></li>
<li><a href="#libopencore_002damrwb" accesskey="8">libopencore-amrwb</a></li>
<li><a href="#libopus" accesskey="9">libopus</a></li>
</ul>
<div class="section-level-extent" id="ac3">
<h3 class="section"><span>5.1 ac3<a class="copiable-link" href="#ac3"> &para;</a></span></h3>

<p>AC-3 audio decoder.
</p>
<p>This decoder implements part of ATSC A/52:2010 and ETSI TS 102 366, as well as
the undocumented RealAudio 3 (a.k.a. dnet).
</p>
<ul class="mini-toc">
<li><a href="#AC_002d3-Decoder-Options" accesskey="1">AC-3 Decoder Options</a></li>
</ul>
<div class="subsection-level-extent" id="AC_002d3-Decoder-Options">
<h4 class="subsection"><span>5.1.1 AC-3 Decoder Options<a class="copiable-link" href="#AC_002d3-Decoder-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-drc_scale <var class="var">value</var></samp></dt>
<dd><p>Dynamic Range Scale Factor. The factor to apply to dynamic range values
from the AC-3 stream. This factor is applied exponentially. The default value is 1.
There are 3 notable scale factor ranges:
</p><dl class="table">
<dt><samp class="option">drc_scale == 0</samp></dt>
<dd><p>DRC disabled. Produces full range audio.
</p></dd>
<dt><samp class="option">0 &lt; drc_scale &lt;= 1</samp></dt>
<dd><p>DRC enabled.  Applies a fraction of the stream DRC value.
Audio reproduction is between full range and full compression.
</p></dd>
<dt><samp class="option">drc_scale &gt; 1</samp></dt>
<dd><p>DRC enabled. Applies drc_scale asymmetrically.
Loud sounds are fully compressed.  Soft sounds are enhanced.
</p></dd>
</dl>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="flac-1">
<h3 class="section"><span>5.2 flac<a class="copiable-link" href="#flac-1"> &para;</a></span></h3>

<p>FLAC audio decoder.
</p>
<p>This decoder aims to implement the complete FLAC specification from Xiph.
</p>
<ul class="mini-toc">
<li><a href="#FLAC-Decoder-options" accesskey="1">FLAC Decoder options</a></li>
</ul>
<div class="subsection-level-extent" id="FLAC-Decoder-options">
<h4 class="subsection"><span>5.2.1 FLAC Decoder options<a class="copiable-link" href="#FLAC-Decoder-options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-use_buggy_lpc</samp></dt>
<dd><p>The lavc FLAC encoder used to produce buggy streams with high lpc values
(like the default value). This option makes it possible to decode such streams
correctly by using lavc&rsquo;s old buggy lpc logic for decoding.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="ffwavesynth">
<h3 class="section"><span>5.3 ffwavesynth<a class="copiable-link" href="#ffwavesynth"> &para;</a></span></h3>

<p>Internal wave synthesizer.
</p>
<p>This decoder generates wave patterns according to predefined sequences. Its
use is purely internal and the format of the data it accepts is not publicly
documented.
</p>
</div>
<div class="section-level-extent" id="libcelt">
<h3 class="section"><span>5.4 libcelt<a class="copiable-link" href="#libcelt"> &para;</a></span></h3>

<p>libcelt decoder wrapper.
</p>
<p>libcelt allows libavcodec to decode the Xiph CELT ultra-low delay audio codec.
Requires the presence of the libcelt headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-libcelt</code>.
</p>
</div>
<div class="section-level-extent" id="libgsm">
<h3 class="section"><span>5.5 libgsm<a class="copiable-link" href="#libgsm"> &para;</a></span></h3>

<p>libgsm decoder wrapper.
</p>
<p>libgsm allows libavcodec to decode the GSM full rate audio codec. Requires
the presence of the libgsm headers and library during configuration. You need
to explicitly configure the build with <code class="code">--enable-libgsm</code>.
</p>
<p>This decoder supports both the ordinary GSM and the Microsoft variant.
</p>
</div>
<div class="section-level-extent" id="libilbc">
<h3 class="section"><span>5.6 libilbc<a class="copiable-link" href="#libilbc"> &para;</a></span></h3>

<p>libilbc decoder wrapper.
</p>
<p>libilbc allows libavcodec to decode the Internet Low Bitrate Codec (iLBC)
audio codec. Requires the presence of the libilbc headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libilbc</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-7" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-7">
<h4 class="subsection"><span>5.6.1 Options<a class="copiable-link" href="#Options-7"> &para;</a></span></h4>

<p>The following option is supported by the libilbc wrapper.
</p>
<dl class="table">
<dt><samp class="option">enhance</samp></dt>
<dd>
<p>Enable the enhancement of the decoded audio when set to 1. The default
value is 0 (disabled).
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libopencore_002damrnb">
<h3 class="section"><span>5.7 libopencore-amrnb<a class="copiable-link" href="#libopencore_002damrnb"> &para;</a></span></h3>

<p>libopencore-amrnb decoder wrapper.
</p>
<p>libopencore-amrnb allows libavcodec to decode the Adaptive Multi-Rate
Narrowband audio codec. Using it requires the presence of the
libopencore-amrnb headers and library during configuration. You need to
explicitly configure the build with <code class="code">--enable-libopencore-amrnb</code>.
</p>
<p>An FFmpeg native decoder for AMR-NB exists, so users can decode AMR-NB
without this library.
</p>
</div>
<div class="section-level-extent" id="libopencore_002damrwb">
<h3 class="section"><span>5.8 libopencore-amrwb<a class="copiable-link" href="#libopencore_002damrwb"> &para;</a></span></h3>

<p>libopencore-amrwb decoder wrapper.
</p>
<p>libopencore-amrwb allows libavcodec to decode the Adaptive Multi-Rate
Wideband audio codec. Using it requires the presence of the
libopencore-amrwb headers and library during configuration. You need to
explicitly configure the build with <code class="code">--enable-libopencore-amrwb</code>.
</p>
<p>An FFmpeg native decoder for AMR-WB exists, so users can decode AMR-WB
without this library.
</p>
</div>
<div class="section-level-extent" id="libopus">
<h3 class="section"><span>5.9 libopus<a class="copiable-link" href="#libopus"> &para;</a></span></h3>

<p>libopus decoder wrapper.
</p>
<p>libopus allows libavcodec to decode the Opus Interactive Audio Codec.
Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libopus</code>.
</p>
<p>An FFmpeg native decoder for Opus exists, so users can decode Opus
without this library.
</p>

</div>
</div>
<div class="chapter-level-extent" id="Subtitles-Decoders">
<h2 class="chapter"><span>6 Subtitles Decoders<a class="copiable-link" href="#Subtitles-Decoders"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#libaribb24" accesskey="1">libaribb24</a></li>
<li><a href="#libaribcaption" accesskey="2">libaribcaption</a></li>
<li><a href="#dvbsub" accesskey="3">dvbsub</a></li>
<li><a href="#dvdsub" accesskey="4">dvdsub</a></li>
<li><a href="#libzvbi_002dteletext" accesskey="5">libzvbi-teletext</a></li>
</ul>
<div class="section-level-extent" id="libaribb24">
<h3 class="section"><span>6.1 libaribb24<a class="copiable-link" href="#libaribb24"> &para;</a></span></h3>

<p>ARIB STD-B24 caption decoder.
</p>
<p>Implements profiles A and C of the ARIB STD-B24 standard.
</p>
<ul class="mini-toc">
<li><a href="#libaribb24-Decoder-Options" accesskey="1">libaribb24 Decoder Options</a></li>
</ul>
<div class="subsection-level-extent" id="libaribb24-Decoder-Options">
<h4 class="subsection"><span>6.1.1 libaribb24 Decoder Options<a class="copiable-link" href="#libaribb24-Decoder-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-aribb24-base-path <var class="var">path</var></samp></dt>
<dd><p>Sets the base path for the libaribb24 library. This is utilized for reading of
configuration files (for custom unicode conversions), and for dumping of
non-text symbols as images under that location.
</p>
<p>Unset by default.
</p>
</dd>
<dt><samp class="option">-aribb24-skip-ruby-text <var class="var">boolean</var></samp></dt>
<dd><p>Tells the decoder wrapper to skip text blocks that contain half-height ruby
text.
</p>
<p>Enabled by default.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libaribcaption">
<h3 class="section"><span>6.2 libaribcaption<a class="copiable-link" href="#libaribcaption"> &para;</a></span></h3>

<p>Yet another ARIB STD-B24 caption decoder using external <em class="dfn">libaribcaption</em>
library.
</p>
<p>Implements profiles A and C of the Japanse ARIB STD-B24 standard,
Brazilian ABNT NBR 15606-1, and Philippines version of ISDB-T.
</p>
<p>Requires the presence of the libaribcaption headers and library
(<a class="url" href="https://github.com/xqq/libaribcaption">https://github.com/xqq/libaribcaption</a>) during configuration.
You need to explicitly configure the build with <code class="code">--enable-libaribcaption</code>.
If both <em class="dfn">libaribb24</em> and <em class="dfn">libaribcaption</em> are enabled, <em class="dfn">libaribcaption</em>
decoder precedes.
</p>
<ul class="mini-toc">
<li><a href="#libaribcaption-Decoder-Options" accesskey="1">libaribcaption Decoder Options</a></li>
<li><a href="#libaribcaption-decoder-usage-examples" accesskey="2">libaribcaption decoder usage examples</a></li>
</ul>
<div class="subsection-level-extent" id="libaribcaption-Decoder-Options">
<h4 class="subsection"><span>6.2.1 libaribcaption Decoder Options<a class="copiable-link" href="#libaribcaption-Decoder-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-sub_type <var class="var">subtitle_type</var></samp></dt>
<dd><p>Specifies the format of the decoded subtitles.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">bitmap</samp>&rsquo;</dt>
<dd><p>Graphical image.
</p></dd>
<dt>&lsquo;<samp class="samp">ass</samp>&rsquo;</dt>
<dd><p>ASS formatted text.
</p></dd>
<dt>&lsquo;<samp class="samp">text</samp>&rsquo;</dt>
<dd><p>Simple text based output without formatting.
</p></dd>
</dl>

<p>The default is <em class="dfn">ass</em> as same as <em class="dfn">libaribb24</em> decoder.
Some present players (e.g., <em class="dfn">mpv</em>) expect ASS format for ARIB caption.
</p>
</dd>
<dt><samp class="option">-caption_encoding <var class="var">encoding_scheme</var></samp></dt>
<dd><p>Specifies the encoding scheme of input subtitle text.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>Automatically detect text encoding (default).
</p></dd>
<dt>&lsquo;<samp class="samp">jis</samp>&rsquo;</dt>
<dd><p>8bit-char JIS encoding defined in ARIB STD B24.
This encoding used in Japan for ISDB captions.
</p></dd>
<dt>&lsquo;<samp class="samp">utf8</samp>&rsquo;</dt>
<dd><p>UTF-8 encoding defined in ARIB STD B24.
This encoding is used in Philippines for ISDB-T captions.
</p></dd>
<dt>&lsquo;<samp class="samp">latin</samp>&rsquo;</dt>
<dd><p>Latin character encoding defined in ABNT NBR 15606-1.
This encoding is used in South America for SBTVD / ISDB-Tb captions.
</p></dd>
</dl>

</dd>
<dt><samp class="option">-font <var class="var">font_name[,font_name2,...]</var></samp></dt>
<dd><p>Specify comma-separated list of font family names to be used for <em class="dfn">bitmap</em>
or <em class="dfn">ass</em> type subtitle rendering.
Only first font name is used for <em class="dfn">ass</em> type subtitle.
</p>
<p>If not specified, use internaly defined default font family.
</p>
</dd>
<dt><samp class="option">-ass_single_rect <var class="var">boolean</var></samp></dt>
<dd><p>ARIB STD-B24 specifies that some captions may be displayed at different
positions at a time (multi-rectangle subtitle).
Since some players (e.g., old <em class="dfn">mpv</em>) can&rsquo;t handle multiple ASS rectangles
in a single AVSubtitle, or multiple ASS rectangles of indeterminate duration
with the same start timestamp, this option can change the behavior so that
all the texts are displayed in a single ASS rectangle.
</p>
<p>The default is <var class="var">false</var>.
</p>
<p>If your player cannot handle AVSubtitles with multiple ASS rectangles properly,
set this option to <var class="var">true</var> or define <code class="env">ASS_SINGLE_RECT=1</code> to change
default behavior at compilation.
</p>
</dd>
<dt><samp class="option">-force_outline_text <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether always render outline text for all characters regardless of
the indication by charactor style.
</p>
<p>The default is <var class="var">false</var>.
</p>
</dd>
<dt><samp class="option">-outline_width <var class="var">number</var> (0.0 - 3.0)</samp></dt>
<dd><p>Specify width for outline text, in dots (relative).
</p>
<p>The default is <var class="var">1.5</var>.
</p>
</dd>
<dt><samp class="option">-ignore_background <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to ignore background color rendering.
</p>
<p>The default is <var class="var">false</var>.
</p>
</dd>
<dt><samp class="option">-ignore_ruby <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to ignore rendering for ruby-like (furigana) characters.
</p>
<p>The default is <var class="var">false</var>.
</p>
</dd>
<dt><samp class="option">-replace_drcs <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to render replaced DRCS characters as Unicode characters.
</p>
<p>The default is <var class="var">true</var>.
</p>
</dd>
<dt><samp class="option">-replace_msz_ascii <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to replace MSZ (Middle Size; half width) fullwidth
alphanumerics with halfwidth alphanumerics.
</p>
<p>The default is <var class="var">true</var>.
</p>
</dd>
<dt><samp class="option">-replace_msz_japanese <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to replace some MSZ (Middle Size; half width) fullwidth
japanese special characters with halfwidth ones.
</p>
<p>The default is <var class="var">true</var>.
</p>
</dd>
<dt><samp class="option">-replace_msz_glyph <var class="var">boolean</var></samp></dt>
<dd><p>Specify whether to replace MSZ (Middle Size; half width) characters
with halfwidth glyphs if the fonts supports it.
This option works under FreeType or DirectWrite renderer
with Adobe-Japan1 compliant fonts.
e.g., IBM Plex Sans JP, Morisawa BIZ UDGothic, Morisawa BIZ UDMincho,
Yu Gothic, Yu Mincho, and Meiryo.
</p>
<p>The default is <var class="var">true</var>.
</p>
</dd>
<dt><samp class="option">-canvas_size <var class="var">image_size</var></samp></dt>
<dd><p>Specify the resolution of the canvas to render subtitles to; usually, this
should be frame size of input video.
This only applies when <code class="code">-subtitle_type</code> is set to <var class="var">bitmap</var>.
</p>
<p>The libaribcaption decoder assumes input frame size for bitmap rendering as below:
</p><ol class="enumerate">
<li> PROFILE_A : 1440 x 1080 with SAR (PAR) 4:3
</li><li> PROFILE_C : 320 x 180 with SAR (PAR) 1:1
</li></ol>

<p>If actual frame size of input video does not match above assumption,
the rendered captions may be distorted.
To make the captions undistorted, add <code class="code">-canvas_size</code> option to specify
actual input video size.
</p>
<p>Note that the <code class="code">-canvas_size</code> option is not required for video with
different size but same aspect ratio.
In such cases, the caption will be stretched or shrunk to actual video size
if <code class="code">-canvas_size</code> option is not specified.
If <code class="code">-canvas_size</code> option is specified with different size,
the caption will be stretched or shrunk as specified size with calculated SAR.
</p>
</dd>
</dl>

</div>
<div class="subsection-level-extent" id="libaribcaption-decoder-usage-examples">
<h4 class="subsection"><span>6.2.2 libaribcaption decoder usage examples<a class="copiable-link" href="#libaribcaption-decoder-usage-examples"> &para;</a></span></h4>

<p>Display MPEG-TS file with ARIB subtitle by <code class="code">ffplay</code> tool:
</p><div class="example">
<pre class="example-preformatted">ffplay -sub_type bitmap MPEG.TS
</pre></div>

<p>Display MPEG-TS file with input frame size 1920x1080 by <code class="code">ffplay</code> tool:
</p><div class="example">
<pre class="example-preformatted">ffplay -sub_type bitmap -canvas_size 1920x1080 MPEG.TS
</pre></div>

<p>Embed ARIB subtitle in transcoded video:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -sub_type bitmap -i src.m2t -filter_complex &quot;[0:v][0:s]overlay&quot; -vcodec h264 dest.mp4
</pre></div>

</div>
</div>
<div class="section-level-extent" id="dvbsub">
<h3 class="section"><span>6.3 dvbsub<a class="copiable-link" href="#dvbsub"> &para;</a></span></h3>

<ul class="mini-toc">
<li><a href="#Options-8" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-8">
<h4 class="subsection"><span>6.3.1 Options<a class="copiable-link" href="#Options-8"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">compute_clut</samp></dt>
<dd><dl class="table">
<dt><samp class="option">-2</samp></dt>
<dd><p>Compute clut once if no matching CLUT is in the stream.
</p></dd>
<dt><samp class="option">-1</samp></dt>
<dd><p>Compute clut if no matching CLUT is in the stream.
</p></dd>
<dt><samp class="option">0</samp></dt>
<dd><p>Never compute CLUT
</p></dd>
<dt><samp class="option">1</samp></dt>
<dd><p>Always compute CLUT and override the one provided in the stream.
</p></dd>
</dl>
</dd>
<dt><samp class="option">dvb_substream</samp></dt>
<dd><p>Selects the dvb substream, or all substreams if -1 which is default.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="dvdsub">
<h3 class="section"><span>6.4 dvdsub<a class="copiable-link" href="#dvdsub"> &para;</a></span></h3>

<p>This codec decodes the bitmap subtitles used in DVDs; the same subtitles can
also be found in VobSub file pairs and in some Matroska files.
</p>
<ul class="mini-toc">
<li><a href="#Options-9" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-9">
<h4 class="subsection"><span>6.4.1 Options<a class="copiable-link" href="#Options-9"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">palette</samp></dt>
<dd><p>Specify the global palette used by the bitmaps. When stored in VobSub, the
palette is normally specified in the index file; in Matroska, the palette is
stored in the codec extra-data in the same format as in VobSub. In DVDs, the
palette is stored in the IFO file, and therefore not available when reading
from dumped VOB files.
</p>
<p>The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example <code class="code">0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b</code>.
</p>
</dd>
<dt><samp class="option">ifo_palette</samp></dt>
<dd><p>Specify the IFO file from which the global palette is obtained.
(experimental)
</p>
</dd>
<dt><samp class="option">forced_subs_only</samp></dt>
<dd><p>Only decode subtitle entries marked as forced. Some titles have forced
and non-forced subtitles in the same track. Setting this flag to <code class="code">1</code>
will only keep the forced subtitles. Default value is <code class="code">0</code>.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libzvbi_002dteletext">
<h3 class="section"><span>6.5 libzvbi-teletext<a class="copiable-link" href="#libzvbi_002dteletext"> &para;</a></span></h3>

<p>Libzvbi allows libavcodec to decode DVB teletext pages and DVB teletext
subtitles. Requires the presence of the libzvbi headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libzvbi</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-10" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-10">
<h4 class="subsection"><span>6.5.1 Options<a class="copiable-link" href="#Options-10"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">txt_page</samp></dt>
<dd><p>List of teletext page numbers to decode. Pages that do not match the specified
list are dropped. You may use the special <code class="code">*</code> string to match all pages,
or <code class="code">subtitle</code> to match all subtitle pages.
Default value is *.
</p></dd>
<dt><samp class="option">txt_default_region</samp></dt>
<dd><p>Set default character set used for decoding, a value between 0 and 87 (see
ETS 300 706, Section 15, Table 32). Default value is -1, which does not
override the libzvbi default. This option is needed for some legacy level 1.0
transmissions which cannot signal the proper charset.
</p></dd>
<dt><samp class="option">txt_chop_top</samp></dt>
<dd><p>Discards the top teletext line. Default value is 1.
</p></dd>
<dt><samp class="option">txt_format</samp></dt>
<dd><p>Specifies the format of the decoded subtitles.
</p><dl class="table">
<dt><samp class="option">bitmap</samp></dt>
<dd><p>The default format, you should use this for teletext pages, because certain
graphics and colors cannot be expressed in simple text or even ASS.
</p></dd>
<dt><samp class="option">text</samp></dt>
<dd><p>Simple text based output without formatting.
</p></dd>
<dt><samp class="option">ass</samp></dt>
<dd><p>Formatted ASS output, subtitle pages and teletext pages are returned in
different styles, subtitle pages are stripped down to text, but an effort is
made to keep the text alignment and the formatting.
</p></dd>
</dl>
</dd>
<dt><samp class="option">txt_left</samp></dt>
<dd><p>X offset of generated bitmaps, default is 0.
</p></dd>
<dt><samp class="option">txt_top</samp></dt>
<dd><p>Y offset of generated bitmaps, default is 0.
</p></dd>
<dt><samp class="option">txt_chop_spaces</samp></dt>
<dd><p>Chops leading and trailing spaces and removes empty lines from the generated
text. This option is useful for teletext based subtitles where empty spaces may
be present at the start or at the end of the lines or empty lines may be
present between the subtitle lines because of double-sized teletext characters.
Default value is 1.
</p></dd>
<dt><samp class="option">txt_duration</samp></dt>
<dd><p>Sets the display duration of the decoded teletext pages or subtitles in
milliseconds. Default value is -1 which means infinity or until the next
subtitle event comes.
</p></dd>
<dt><samp class="option">txt_transparent</samp></dt>
<dd><p>Force transparent background of the generated teletext bitmaps. Default value
is 0 which means an opaque background.
</p></dd>
<dt><samp class="option">txt_opacity</samp></dt>
<dd><p>Sets the opacity (0-255) of the teletext background. If
<samp class="option">txt_transparent</samp> is not set, it only affects characters between a start
box and an end box, typically subtitles. Default value is 0 if
<samp class="option">txt_transparent</samp> is set, 255 otherwise.
</p>
</dd>
</dl>

</div>
</div>
</div>
<div class="chapter-level-extent" id="Encoders">
<h2 class="chapter"><span>7 Encoders<a class="copiable-link" href="#Encoders"> &para;</a></span></h2>

<p>Encoders are configured elements in FFmpeg which allow the encoding of
multimedia streams.
</p>
<p>When you configure your FFmpeg build, all the supported native encoders
are enabled by default. Encoders requiring an external library must be enabled
manually via the corresponding <code class="code">--enable-lib</code> option. You can list all
available encoders using the configure option <code class="code">--list-encoders</code>.
</p>
<p>You can disable all the encoders with the configure option
<code class="code">--disable-encoders</code> and selectively enable / disable single encoders
with the options <code class="code">--enable-encoder=<var class="var">ENCODER</var></code> /
<code class="code">--disable-encoder=<var class="var">ENCODER</var></code>.
</p>
<p>The option <code class="code">-encoders</code> of the ff* tools will display the list of
enabled encoders.
</p>

</div>
<div class="chapter-level-extent" id="Audio-Encoders">
<h2 class="chapter"><span>8 Audio Encoders<a class="copiable-link" href="#Audio-Encoders"> &para;</a></span></h2>

<p>A description of some of the currently available audio encoders
follows.
</p>
<a class="anchor" id="aacenc"></a><ul class="mini-toc">
<li><a href="#aac" accesskey="1">aac</a></li>
<li><a href="#ac3-and-ac3_005ffixed" accesskey="2">ac3 and ac3_fixed</a></li>
<li><a href="#flac-2" accesskey="3">flac</a></li>
<li><a href="#opus" accesskey="4">opus</a></li>
<li><a href="#libfdk_005faac" accesskey="5">libfdk_aac</a></li>
<li><a href="#liblc3" accesskey="6">liblc3</a></li>
<li><a href="#libmp3lame-1" accesskey="7">libmp3lame</a></li>
<li><a href="#libopencore_002damrnb-1" accesskey="8">libopencore-amrnb</a></li>
<li><a href="#libopus-1" accesskey="9">libopus</a></li>
<li><a href="#libshine-1">libshine</a></li>
<li><a href="#libtwolame">libtwolame</a></li>
<li><a href="#libvo_002damrwbenc">libvo-amrwbenc</a></li>
<li><a href="#libvorbis">libvorbis</a></li>
<li><a href="#mjpeg">mjpeg</a></li>
<li><a href="#wavpack">wavpack</a></li>
</ul>
<div class="section-level-extent" id="aac">
<h3 class="section"><span>8.1 aac<a class="copiable-link" href="#aac"> &para;</a></span></h3>

<p>Advanced Audio Coding (AAC) encoder.
</p>
<p>This encoder is the default AAC encoder, natively implemented into FFmpeg.
</p>
<ul class="mini-toc">
<li><a href="#Options-11" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-11">
<h4 class="subsection"><span>8.1.1 Options<a class="copiable-link" href="#Options-11"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bit rate in bits/s. Setting this automatically activates constant bit rate
(CBR) mode. If this option is unspecified it is set to 128kbps.
</p>
</dd>
<dt><samp class="option">q</samp></dt>
<dd><p>Set quality for variable bit rate (VBR) mode. This option is valid only using
the <code class="command">ffmpeg</code> command-line tool. For library interface users, use
<samp class="option">global_quality</samp>.
</p>
</dd>
<dt><samp class="option">cutoff</samp></dt>
<dd><p>Set cutoff frequency. If unspecified will allow the encoder to dynamically
adjust the cutoff to improve clarity on low bitrates.
</p>
</dd>
<dt><samp class="option">aac_coder</samp></dt>
<dd><p>Set AAC encoder coding method. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">twoloop</samp>&rsquo;</dt>
<dd><p>Two loop searching (TLS) method. This is the default method.
</p>
<p>This method first sets quantizers depending on band thresholds and then tries
to find an optimal combination by adding or subtracting a specific value from
all quantizers and adjusting some individual quantizer a little.  Will tune
itself based on whether <samp class="option">aac_is</samp>, <samp class="option">aac_ms</samp> and <samp class="option">aac_pns</samp>
are enabled.
</p>
</dd>
<dt>&lsquo;<samp class="samp">anmr</samp>&rsquo;</dt>
<dd><p>Average noise to mask ratio (ANMR) trellis-based solution.
</p>
<p>This is an experimental coder which currently produces a lower quality, is more
unstable and is slower than the default twoloop coder but has potential.
Currently has no support for the <samp class="option">aac_is</samp> or <samp class="option">aac_pns</samp> options.
Not currently recommended.
</p>
</dd>
<dt>&lsquo;<samp class="samp">fast</samp>&rsquo;</dt>
<dd><p>Constant quantizer method.
</p>
<p>Uses a cheaper version of twoloop algorithm that doesn&rsquo;t try to do as many
clever adjustments. Worse with low bitrates (less than 64kbps), but is better
and much faster at higher bitrates.
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">aac_ms</samp></dt>
<dd><p>Sets mid/side coding mode. The default value of &quot;auto&quot; will automatically use
M/S with bands which will benefit from such coding. Can be forced for all bands
using the value &quot;enable&quot;, which is mainly useful for debugging or disabled using
&quot;disable&quot;.
</p>
</dd>
<dt><samp class="option">aac_is</samp></dt>
<dd><p>Sets intensity stereo coding tool usage. By default, it&rsquo;s enabled and will
automatically toggle IS for similar pairs of stereo bands if it&rsquo;s beneficial.
Can be disabled for debugging by setting the value to &quot;disable&quot;.
</p>
</dd>
<dt><samp class="option">aac_pns</samp></dt>
<dd><p>Uses perceptual noise substitution to replace low entropy high frequency bands
with imperceptible white noise during the decoding process. By default, it&rsquo;s
enabled, but can be disabled for debugging purposes by using &quot;disable&quot;.
</p>
</dd>
<dt><samp class="option">aac_tns</samp></dt>
<dd><p>Enables the use of a multitap FIR filter which spans through the high frequency
bands to hide quantization noise during the encoding process and is reverted
by the decoder. As well as decreasing unpleasant artifacts in the high range
this also reduces the entropy in the high bands and allows for more bits to
be used by the mid-low bands. By default it&rsquo;s enabled but can be disabled for
debugging by setting the option to &quot;disable&quot;.
</p>
</dd>
<dt><samp class="option">aac_ltp</samp></dt>
<dd><p>Enables the use of the long term prediction extension which increases coding
efficiency in very low bandwidth situations such as encoding of voice or
solo piano music by extending constant harmonic peaks in bands throughout
frames. This option is implied by profile:a aac_low and is incompatible with
aac_pred. Use in conjunction with <samp class="option">-ar</samp> to decrease the samplerate.
</p>
</dd>
<dt><samp class="option">aac_pred</samp></dt>
<dd><p>Enables the use of a more traditional style of prediction where the spectral
coefficients transmitted are replaced by the difference of the current
coefficients minus the previous &quot;predicted&quot; coefficients. In theory and sometimes
in practice this can improve quality for low to mid bitrate audio.
This option implies the aac_main profile and is incompatible with aac_ltp.
</p>
</dd>
<dt><samp class="option">profile</samp></dt>
<dd><p>Sets the encoding profile, possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">aac_low</samp>&rsquo;</dt>
<dd><p>The default, AAC &quot;Low-complexity&quot; profile. Is the most compatible and produces
decent quality.
</p>
</dd>
<dt>&lsquo;<samp class="samp">mpeg2_aac_low</samp>&rsquo;</dt>
<dd><p>Equivalent to <code class="code">-profile:a aac_low -aac_pns 0</code>. PNS was introduced with the
MPEG4 specifications.
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_ltp</samp>&rsquo;</dt>
<dd><p>Long term prediction profile, is enabled by and will enable the <samp class="option">aac_ltp</samp>
option. Introduced in MPEG4.
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_main</samp>&rsquo;</dt>
<dd><p>Main-type prediction profile, is enabled by and will enable the <samp class="option">aac_pred</samp>
option. Introduced in MPEG2.
</p>
</dd>
</dl>
<p>If this option is unspecified it is set to &lsquo;<samp class="samp">aac_low</samp>&rsquo;.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="ac3-and-ac3_005ffixed">
<h3 class="section"><span>8.2 ac3 and ac3_fixed<a class="copiable-link" href="#ac3-and-ac3_005ffixed"> &para;</a></span></h3>

<p>AC-3 audio encoders.
</p>
<p>These encoders implement part of ATSC A/52:2010 and ETSI TS 102 366.
</p>
<p>The <var class="var">ac3</var> encoder uses floating-point math, while the <var class="var">ac3_fixed</var>
encoder only uses fixed-point integer math. This does not mean that one is
always faster, just that one or the other may be better suited to a
particular system. The <var class="var">ac3_fixed</var> encoder is not the default codec for
any of the output formats, so it must be specified explicitly using the option
<code class="code">-acodec ac3_fixed</code> in order to use it.
</p>
<ul class="mini-toc">
<li><a href="#AC_002d3-Metadata" accesskey="1">AC-3 Metadata</a></li>
<li><a href="#Extended-Bitstream-Information" accesskey="2">Extended Bitstream Information</a></li>
<li><a href="#Other-AC_002d3-Encoding-Options" accesskey="3">Other AC-3 Encoding Options</a></li>
<li><a href="#Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options" accesskey="4">Floating-Point-Only AC-3 Encoding Options</a></li>
</ul>
<div class="subsection-level-extent" id="AC_002d3-Metadata">
<h4 class="subsection"><span>8.2.1 AC-3 Metadata<a class="copiable-link" href="#AC_002d3-Metadata"> &para;</a></span></h4>

<p>The AC-3 metadata options are used to set parameters that describe the audio,
but in most cases do not affect the audio encoding itself. Some of the options
do directly affect or influence the decoding and playback of the resulting
bitstream, while others are just for informational purposes. A few of the
options will add bits to the output stream that could otherwise be used for
audio data, and will thus affect the quality of the output. Those will be
indicated accordingly with a note in the option list below.
</p>
<p>These parameters are described in detail in several publicly-available
documents.
</p><ul class="itemize mark-bullet">
<li><a class="uref" href="http://www.atsc.org/cms/standards/a_52-2010.pdf">A/52:2010 - Digital Audio Compression (AC-3) (E-AC-3) Standard</a>
</li><li><a class="uref" href="http://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf">A/54 - Guide to the Use of the ATSC Digital Television Standard</a>
</li><li><a class="uref" href="http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf">Dolby Metadata Guide</a>
</li><li><a class="uref" href="http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf">Dolby Digital Professional Encoding Guidelines</a>
</li></ul>

<ul class="mini-toc">
<li><a href="#Metadata-Control-Options" accesskey="1">Metadata Control Options</a></li>
<li><a href="#Downmix-Levels" accesskey="2">Downmix Levels</a></li>
<li><a href="#Audio-Production-Information" accesskey="3">Audio Production Information</a></li>
<li><a href="#Other-Metadata-Options" accesskey="4">Other Metadata Options</a></li>
</ul>
<div class="subsubsection-level-extent" id="Metadata-Control-Options">
<h4 class="subsubsection"><span>8.2.1.1 Metadata Control Options<a class="copiable-link" href="#Metadata-Control-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-per_frame_metadata <var class="var">boolean</var></samp></dt>
<dd><p>Allow Per-Frame Metadata. Specifies if the encoder should check for changing
metadata for each frame.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dd><p>The metadata values set at initialization will be used for every frame in the
stream. (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dd><p>Metadata values can be changed before encoding each frame.
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsubsection-level-extent" id="Downmix-Levels">
<h4 class="subsubsection"><span>8.2.1.2 Downmix Levels<a class="copiable-link" href="#Downmix-Levels"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-center_mixlev <var class="var">level</var></samp></dt>
<dd><p>Center Mix Level. The amount of gain the decoder should apply to the center
channel when downmixing to stereo. This field will only be written to the
bitstream if a center channel is present. The value is specified as a scale
factor. There are 3 valid values:
</p><dl class="table">
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3dB gain
</p></dd>
<dt><samp class="option">0.595</samp></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6dB gain
</p></dd>
</dl>

</dd>
<dt><samp class="option">-surround_mixlev <var class="var">level</var></samp></dt>
<dd><p>Surround Mix Level. The amount of gain the decoder should apply to the surround
channel(s) when downmixing to stereo. This field will only be written to the
bitstream if one or more surround channels are present. The value is specified
as a scale factor.  There are 3 valid values:
</p><dl class="table">
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3dB gain
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6dB gain (default)
</p></dd>
<dt><samp class="option">0.000</samp></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsubsection-level-extent" id="Audio-Production-Information">
<h4 class="subsubsection"><span>8.2.1.3 Audio Production Information<a class="copiable-link" href="#Audio-Production-Information"> &para;</a></span></h4>
<p>Audio Production Information is optional information describing the mixing
environment.  Either none or both of the fields are written to the bitstream.
</p>
<dl class="table">
<dt><samp class="option">-mixing_level <var class="var">number</var></samp></dt>
<dd><p>Mixing Level. Specifies peak sound pressure level (SPL) in the production
environment when the mix was mastered. Valid values are 80 to 111, or -1 for
unknown or not indicated. The default value is -1, but that value cannot be
used if the Audio Production Information is written to the bitstream. Therefore,
if the <code class="code">room_type</code> option is not the default value, the <code class="code">mixing_level</code>
option must not be -1.
</p>
</dd>
<dt><samp class="option">-room_type <var class="var">type</var></samp></dt>
<dd><p>Room Type. Describes the equalization used during the final mixing session at
the studio or on the dubbing stage. A large room is a dubbing stage with the
industry standard X-curve equalization; a small room has flat equalization.
This field will not be written to the bitstream if both the <code class="code">mixing_level</code>
option and the <code class="code">room_type</code> option have the default values.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">notindicated</samp></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">large</samp></dt>
<dd><p>Large Room
</p></dd>
<dt><samp class="option">2</samp></dt>
<dt><samp class="option">small</samp></dt>
<dd><p>Small Room
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsubsection-level-extent" id="Other-Metadata-Options">
<h4 class="subsubsection"><span>8.2.1.4 Other Metadata Options<a class="copiable-link" href="#Other-Metadata-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-copyright <var class="var">boolean</var></samp></dt>
<dd><p>Copyright Indicator. Specifies whether a copyright exists for this audio.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>No Copyright Exists (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Copyright Exists
</p></dd>
</dl>

</dd>
<dt><samp class="option">-dialnorm <var class="var">value</var></samp></dt>
<dd><p>Dialogue Normalization. Indicates how far the average dialogue level of the
program is below digital 100% full scale (0 dBFS). This parameter determines a
level shift during audio reproduction that sets the average volume of the
dialogue to a preset level. The goal is to match volume level between program
sources. A value of -31dB will result in no volume level change, relative to
the source volume, during audio reproduction. Valid values are whole numbers in
the range -31 to -1, with -31 being the default.
</p>
</dd>
<dt><samp class="option">-dsur_mode <var class="var">mode</var></samp></dt>
<dd><p>Dolby Surround Mode. Specifies whether the stereo signal uses Dolby Surround
(Pro Logic). This field will only be written to the bitstream if the audio
stream is stereo. Using this option does <b class="b">NOT</b> mean the encoder will actually
apply Dolby Surround processing.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">notindicated</samp></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>Not Dolby Surround Encoded
</p></dd>
<dt><samp class="option">2</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Dolby Surround Encoded
</p></dd>
</dl>

</dd>
<dt><samp class="option">-original <var class="var">boolean</var></samp></dt>
<dd><p>Original Bit Stream Indicator. Specifies whether this audio is from the
original source and not a copy.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>Not Original Source
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Original Source (default)
</p></dd>
</dl>

</dd>
</dl>

</div>
</div>
<div class="subsection-level-extent" id="Extended-Bitstream-Information">
<h4 class="subsection"><span>8.2.2 Extended Bitstream Information<a class="copiable-link" href="#Extended-Bitstream-Information"> &para;</a></span></h4>
<p>The extended bitstream options are part of the Alternate Bit Stream Syntax as
specified in Annex D of the A/52:2010 standard. It is grouped into 2 parts.
If any one parameter in a group is specified, all values in that group will be
written to the bitstream.  Default values are used for those that are written
but have not been specified.  If the mixing levels are written, the decoder
will use these values instead of the ones specified in the <code class="code">center_mixlev</code>
and <code class="code">surround_mixlev</code> options if it supports the Alternate Bit Stream
Syntax.
</p>
<ul class="mini-toc">
<li><a href="#Extended-Bitstream-Information-_002d-Part-1" accesskey="1">Extended Bitstream Information - Part 1</a></li>
<li><a href="#Extended-Bitstream-Information-_002d-Part-2" accesskey="2">Extended Bitstream Information - Part 2</a></li>
</ul>
<div class="subsubsection-level-extent" id="Extended-Bitstream-Information-_002d-Part-1">
<h4 class="subsubsection"><span>8.2.2.1 Extended Bitstream Information - Part 1<a class="copiable-link" href="#Extended-Bitstream-Information-_002d-Part-1"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-dmix_mode <var class="var">mode</var></samp></dt>
<dd><p>Preferred Stereo Downmix Mode. Allows the user to select either Lt/Rt
(Dolby Surround) or Lo/Ro (normal stereo) as the preferred stereo downmix mode.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">notindicated</samp></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">ltrt</samp></dt>
<dd><p>Lt/Rt Downmix Preferred
</p></dd>
<dt><samp class="option">2</samp></dt>
<dt><samp class="option">loro</samp></dt>
<dd><p>Lo/Ro Downmix Preferred
</p></dd>
</dl>

</dd>
<dt><samp class="option">-ltrt_cmixlev <var class="var">level</var></samp></dt>
<dd><p>Lt/Rt Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lt/Rt mode.
</p><dl class="table">
<dt><samp class="option">1.414</samp></dt>
<dd><p>Apply +3dB gain
</p></dd>
<dt><samp class="option">1.189</samp></dt>
<dd><p>Apply +1.5dB gain
</p></dd>
<dt><samp class="option">1.000</samp></dt>
<dd><p>Apply 0dB gain
</p></dd>
<dt><samp class="option">0.841</samp></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><samp class="option">0.595</samp></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6.0dB gain
</p></dd>
<dt><samp class="option">0.000</samp></dt>
<dd><p>Silence Center Channel
</p></dd>
</dl>

</dd>
<dt><samp class="option">-ltrt_surmixlev <var class="var">level</var></samp></dt>
<dd><p>Lt/Rt Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lt/Rt mode.
</p><dl class="table">
<dt><samp class="option">0.841</samp></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><samp class="option">0.595</samp></dt>
<dd><p>Apply -4.5dB gain
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6.0dB gain (default)
</p></dd>
<dt><samp class="option">0.000</samp></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
<dt><samp class="option">-loro_cmixlev <var class="var">level</var></samp></dt>
<dd><p>Lo/Ro Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lo/Ro mode.
</p><dl class="table">
<dt><samp class="option">1.414</samp></dt>
<dd><p>Apply +3dB gain
</p></dd>
<dt><samp class="option">1.189</samp></dt>
<dd><p>Apply +1.5dB gain
</p></dd>
<dt><samp class="option">1.000</samp></dt>
<dd><p>Apply 0dB gain
</p></dd>
<dt><samp class="option">0.841</samp></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><samp class="option">0.595</samp></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6.0dB gain
</p></dd>
<dt><samp class="option">0.000</samp></dt>
<dd><p>Silence Center Channel
</p></dd>
</dl>

</dd>
<dt><samp class="option">-loro_surmixlev <var class="var">level</var></samp></dt>
<dd><p>Lo/Ro Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lo/Ro mode.
</p><dl class="table">
<dt><samp class="option">0.841</samp></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><samp class="option">0.707</samp></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><samp class="option">0.595</samp></dt>
<dd><p>Apply -4.5dB gain
</p></dd>
<dt><samp class="option">0.500</samp></dt>
<dd><p>Apply -6.0dB gain (default)
</p></dd>
<dt><samp class="option">0.000</samp></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsubsection-level-extent" id="Extended-Bitstream-Information-_002d-Part-2">
<h4 class="subsubsection"><span>8.2.2.2 Extended Bitstream Information - Part 2<a class="copiable-link" href="#Extended-Bitstream-Information-_002d-Part-2"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-dsurex_mode <var class="var">mode</var></samp></dt>
<dd><p>Dolby Surround EX Mode. Indicates whether the stream uses Dolby Surround EX
(7.1 matrixed to 5.1). Using this option does <b class="b">NOT</b> mean the encoder will actually
apply Dolby Surround EX processing.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">notindicated</samp></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Dolby Surround EX Off
</p></dd>
<dt><samp class="option">2</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>Dolby Surround EX On
</p></dd>
</dl>

</dd>
<dt><samp class="option">-dheadphone_mode <var class="var">mode</var></samp></dt>
<dd><p>Dolby Headphone Mode. Indicates whether the stream uses Dolby Headphone
encoding (multi-channel matrixed to 2.0 for use with headphones). Using this
option does <b class="b">NOT</b> mean the encoder will actually apply Dolby Headphone
processing.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">notindicated</samp></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Dolby Headphone Off
</p></dd>
<dt><samp class="option">2</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>Dolby Headphone On
</p></dd>
</dl>

</dd>
<dt><samp class="option">-ad_conv_type <var class="var">type</var></samp></dt>
<dd><p>A/D Converter Type. Indicates whether the audio has passed through HDCD A/D
conversion.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">standard</samp></dt>
<dd><p>Standard A/D Converter (default)
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">hdcd</samp></dt>
<dd><p>HDCD A/D Converter
</p></dd>
</dl>

</dd>
</dl>

</div>
</div>
<div class="subsection-level-extent" id="Other-AC_002d3-Encoding-Options">
<h4 class="subsection"><span>8.2.3 Other AC-3 Encoding Options<a class="copiable-link" href="#Other-AC_002d3-Encoding-Options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-stereo_rematrixing <var class="var">boolean</var></samp></dt>
<dd><p>Stereo Rematrixing. Enables/Disables use of rematrixing for stereo input. This
is an optional AC-3 feature that increases quality by selectively encoding
the left/right channels as mid/side. This option is enabled by default, and it
is highly recommended that it be left as enabled except for testing purposes.
</p>
</dd>
<dt><samp class="option">cutoff <var class="var">frequency</var></samp></dt>
<dd><p>Set lowpass cutoff frequency. If unspecified, the encoder selects a default
determined by various other encoding parameters.
</p>
</dd>
</dl>

</div>
<div class="subsection-level-extent" id="Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options">
<h4 class="subsection"><span>8.2.4 Floating-Point-Only AC-3 Encoding Options<a class="copiable-link" href="#Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options"> &para;</a></span></h4>

<p>These options are only valid for the floating-point encoder and do not exist
for the fixed-point encoder due to the corresponding features not being
implemented in fixed-point.
</p>
<dl class="table">
<dt><samp class="option">-channel_coupling <var class="var">boolean</var></samp></dt>
<dd><p>Enables/Disables use of channel coupling, which is an optional AC-3 feature
that increases quality by combining high frequency information from multiple
channels into a single channel. The per-channel high frequency information is
sent with less accuracy in both the frequency and time domains. This allows
more bits to be used for lower frequencies while preserving enough information
to reconstruct the high frequencies. This option is enabled by default for the
floating-point encoder and should generally be left as enabled except for
testing purposes or to increase encoding speed.
</p><dl class="table">
<dt><samp class="option">-1</samp></dt>
<dt><samp class="option">auto</samp></dt>
<dd><p>Selected by Encoder (default)
</p></dd>
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">off</samp></dt>
<dd><p>Disable Channel Coupling
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">on</samp></dt>
<dd><p>Enable Channel Coupling
</p></dd>
</dl>

</dd>
<dt><samp class="option">-cpl_start_band <var class="var">number</var></samp></dt>
<dd><p>Coupling Start Band. Sets the channel coupling start band, from 1 to 15. If a
value higher than the bandwidth is used, it will be reduced to 1 less than the
coupling end band. If <var class="var">auto</var> is used, the start band will be determined by
the encoder based on the bit rate, sample rate, and channel layout. This option
has no effect if channel coupling is disabled.
</p><dl class="table">
<dt><samp class="option">-1</samp></dt>
<dt><samp class="option">auto</samp></dt>
<dd><p>Selected by Encoder (default)
</p></dd>
</dl>

</dd>
</dl>

<a class="anchor" id="flac"></a></div>
</div>
<div class="section-level-extent" id="flac-2">
<h3 class="section"><span>8.3 flac<a class="copiable-link" href="#flac-2"> &para;</a></span></h3>

<p>FLAC (Free Lossless Audio Codec) Encoder
</p>
<ul class="mini-toc">
<li><a href="#Options-12" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-12">
<h4 class="subsection"><span>8.3.1 Options<a class="copiable-link" href="#Options-12"> &para;</a></span></h4>

<p>The following options are supported by FFmpeg&rsquo;s flac encoder.
</p>
<dl class="table">
<dt><samp class="option">compression_level</samp></dt>
<dd><p>Sets the compression level, which chooses defaults for many other options
if they are not set explicitly. Valid values are from 0 to 12, 5 is the
default.
</p>
</dd>
<dt><samp class="option">frame_size</samp></dt>
<dd><p>Sets the size of the frames in samples per channel.
</p>
</dd>
<dt><samp class="option">lpc_coeff_precision</samp></dt>
<dd><p>Sets the LPC coefficient precision, valid values are from 1 to 15, 15 is the
default.
</p>
</dd>
<dt><samp class="option">lpc_type</samp></dt>
<dd><p>Sets the first stage LPC algorithm
</p><dl class="table">
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dd><p>LPC is not used
</p>
</dd>
<dt>&lsquo;<samp class="samp">fixed</samp>&rsquo;</dt>
<dd><p>fixed LPC coefficients
</p>
</dd>
<dt>&lsquo;<samp class="samp">levinson</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cholesky</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">lpc_passes</samp></dt>
<dd><p>Number of passes to use for Cholesky factorization during LPC analysis
</p>
</dd>
<dt><samp class="option">min_partition_order</samp></dt>
<dd><p>The minimum partition order
</p>
</dd>
<dt><samp class="option">max_partition_order</samp></dt>
<dd><p>The maximum partition order
</p>
</dd>
<dt><samp class="option">prediction_order_method</samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">estimation</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">2level</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">4level</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">8level</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">search</samp>&rsquo;</dt>
<dd><p>Bruteforce search
</p></dd>
<dt>&lsquo;<samp class="samp">log</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">ch_mode</samp></dt>
<dd><p>Channel mode
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>The mode is chosen automatically for each frame
</p></dd>
<dt>&lsquo;<samp class="samp">indep</samp>&rsquo;</dt>
<dd><p>Channels are independently coded
</p></dd>
<dt>&lsquo;<samp class="samp">left_side</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">right_side</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">mid_side</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">exact_rice_parameters</samp></dt>
<dd><p>Chooses if rice parameters are calculated exactly or approximately.
if set to 1 then they are chosen exactly, which slows the code down slightly and
improves compression slightly.
</p>
</dd>
<dt><samp class="option">multi_dim_quant</samp></dt>
<dd><p>Multi Dimensional Quantization. If set to 1 then a 2nd stage LPC algorithm is
applied after the first stage to finetune the coefficients. This is quite slow
and slightly improves compression.
</p>
</dd>
</dl>

<a class="anchor" id="opusenc"></a></div>
</div>
<div class="section-level-extent" id="opus">
<h3 class="section"><span>8.4 opus<a class="copiable-link" href="#opus"> &para;</a></span></h3>

<p>Opus encoder.
</p>
<p>This is a native FFmpeg encoder for the Opus format. Currently, it&rsquo;s in development and
only implements the CELT part of the codec. Its quality is usually worse and at best
is equal to the libopus encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-13" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-13">
<h4 class="subsection"><span>8.4.1 Options<a class="copiable-link" href="#Options-13"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bit rate in bits/s. If unspecified it uses the number of channels and the layout
to make a good guess.
</p>
</dd>
<dt><samp class="option">opus_delay</samp></dt>
<dd><p>Sets the maximum delay in milliseconds. Lower delays than 20ms will very quickly
decrease quality.
</p></dd>
</dl>

<a class="anchor" id="libfdk_002daac_002denc"></a></div>
</div>
<div class="section-level-extent" id="libfdk_005faac">
<h3 class="section"><span>8.5 libfdk_aac<a class="copiable-link" href="#libfdk_005faac"> &para;</a></span></h3>

<p>libfdk-aac AAC (Advanced Audio Coding) encoder wrapper.
</p>
<p>The libfdk-aac library is based on the Fraunhofer FDK AAC code from
the Android project.
</p>
<p>Requires the presence of the libfdk-aac headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libfdk-aac</code>. The library is also incompatible with GPL,
so if you allow the use of GPL, you should configure with
<code class="code">--enable-gpl --enable-nonfree --enable-libfdk-aac</code>.
</p>
<p>This encoder has support for the AAC-HE profiles.
</p>
<p>VBR encoding, enabled through the <samp class="option">vbr</samp> or <samp class="option">flags
+qscale</samp> options, is experimental and only works with some
combinations of parameters.
</p>
<p>Support for encoding 7.1 audio is only available with libfdk-aac 0.1.3 or
higher.
</p>
<p>For more information see the fdk-aac project at
<a class="url" href="http://sourceforge.net/p/opencore-amr/fdk-aac/">http://sourceforge.net/p/opencore-amr/fdk-aac/</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-14" accesskey="1">Options</a></li>
<li><a href="#Examples" accesskey="2">Examples</a></li>
</ul>
<div class="subsection-level-extent" id="Options-14">
<h4 class="subsection"><span>8.5.1 Options<a class="copiable-link" href="#Options-14"> &para;</a></span></h4>

<p>The following options are mapped on the shared FFmpeg codec options.
</p>
<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bit rate in bits/s. If the bitrate is not explicitly specified, it
is automatically set to a suitable value depending on the selected
profile.
</p>
<p>In case VBR mode is enabled the option is ignored.
</p>
</dd>
<dt><samp class="option">ar</samp></dt>
<dd><p>Set audio sampling rate (in Hz).
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of audio channels.
</p>
</dd>
<dt><samp class="option">flags +qscale</samp></dt>
<dd><p>Enable fixed quality, VBR (Variable Bit Rate) mode.
Note that VBR is implicitly enabled when the <samp class="option">vbr</samp> value is
positive.
</p>
</dd>
<dt><samp class="option">cutoff</samp></dt>
<dd><p>Set cutoff frequency. If not specified (or explicitly set to 0) it
will use a value automatically computed by the library. Default value
is 0.
</p>
</dd>
<dt><samp class="option">profile</samp></dt>
<dd><p>Set audio profile.
</p>
<p>The following profiles are recognized:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">aac_low</samp>&rsquo;</dt>
<dd><p>Low Complexity AAC (LC)
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_he</samp>&rsquo;</dt>
<dd><p>High Efficiency AAC (HE-AAC)
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_he_v2</samp>&rsquo;</dt>
<dd><p>High Efficiency AAC version 2 (HE-AACv2)
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_ld</samp>&rsquo;</dt>
<dd><p>Low Delay AAC (LD)
</p>
</dd>
<dt>&lsquo;<samp class="samp">aac_eld</samp>&rsquo;</dt>
<dd><p>Enhanced Low Delay AAC (ELD)
</p></dd>
</dl>

<p>If not specified it is set to &lsquo;<samp class="samp">aac_low</samp>&rsquo;.
</p></dd>
</dl>

<p>The following are private options of the libfdk_aac encoder.
</p>
<dl class="table">
<dt><samp class="option">afterburner</samp></dt>
<dd><p>Enable afterburner feature if set to 1, disabled if set to 0. This
improves the quality but also the required processing power.
</p>
<p>Default value is 1.
</p>
</dd>
<dt><samp class="option">eld_sbr</samp></dt>
<dd><p>Enable SBR (Spectral Band Replication) for ELD if set to 1, disabled
if set to 0.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">eld_v2</samp></dt>
<dd><p>Enable ELDv2 (LD-MPS extension for ELD stereo signals) for ELDv2 if set to 1,
disabled if set to 0.
</p>
<p>Note that option is available when fdk-aac version (AACENCODER_LIB_VL0.AACENCODER_LIB_VL1.AACENCODER_LIB_VL2) &gt; (4.0.0).
</p>
<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">signaling</samp></dt>
<dd><p>Set SBR/PS signaling style.
</p>
<p>It can assume one of the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dd><p>choose signaling implicitly (explicit hierarchical by default,
implicit if global header is disabled)
</p>
</dd>
<dt>&lsquo;<samp class="samp">implicit</samp>&rsquo;</dt>
<dd><p>implicit backwards compatible signaling
</p>
</dd>
<dt>&lsquo;<samp class="samp">explicit_sbr</samp>&rsquo;</dt>
<dd><p>explicit SBR, implicit PS signaling
</p>
</dd>
<dt>&lsquo;<samp class="samp">explicit_hierarchical</samp>&rsquo;</dt>
<dd><p>explicit hierarchical signaling
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">default</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">latm</samp></dt>
<dd><p>Output LATM/LOAS encapsulated data if set to 1, disabled if set to 0.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">header_period</samp></dt>
<dd><p>Set StreamMuxConfig and PCE repetition period (in frames) for sending
in-band configuration buffers within LATM/LOAS transport layer.
</p>
<p>Must be a 16-bits non-negative integer.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">vbr</samp></dt>
<dd><p>Set VBR mode, from 1 to 5. 1 is lowest quality (though still pretty
good) and 5 is highest quality. A value of 0 will disable VBR, and CBR
(Constant Bit Rate) is enabled.
</p>
<p>Currently only the &lsquo;<samp class="samp">aac_low</samp>&rsquo; profile supports VBR encoding.
</p>
<p>VBR modes 1-5 correspond to roughly the following average bit rates:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">1</samp>&rsquo;</dt>
<dd><p>32 kbps/channel
</p></dd>
<dt>&lsquo;<samp class="samp">2</samp>&rsquo;</dt>
<dd><p>40 kbps/channel
</p></dd>
<dt>&lsquo;<samp class="samp">3</samp>&rsquo;</dt>
<dd><p>48-56 kbps/channel
</p></dd>
<dt>&lsquo;<samp class="samp">4</samp>&rsquo;</dt>
<dd><p>64 kbps/channel
</p></dd>
<dt>&lsquo;<samp class="samp">5</samp>&rsquo;</dt>
<dd><p>about 80-96 kbps/channel
</p></dd>
</dl>

<p>Default value is 0.
</p>
</dd>
<dt><samp class="option">frame_length</samp></dt>
<dd><p>Set the audio frame length in samples. Default value is the internal
default of the library. Refer to the library&rsquo;s documentation for information
about supported values.
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="Examples">
<h4 class="subsection"><span>8.5.2 Examples<a class="copiable-link" href="#Examples"> &para;</a></span></h4>

<ul class="itemize mark-bullet">
<li>Use <code class="command">ffmpeg</code> to convert an audio file to VBR AAC in an M4A (MP4)
container:
<div class="example">
<pre class="example-preformatted">ffmpeg -i input.wav -codec:a libfdk_aac -vbr 3 output.m4a
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to convert an audio file to CBR 64k kbps AAC, using the
High-Efficiency AAC profile:
<div class="example">
<pre class="example-preformatted">ffmpeg -i input.wav -c:a libfdk_aac -profile:a aac_he -b:a 64k output.m4a
</pre></div>
</li></ul>

<a class="anchor" id="liblc3_002denc"></a></div>
</div>
<div class="section-level-extent" id="liblc3">
<h3 class="section"><span>8.6 liblc3<a class="copiable-link" href="#liblc3"> &para;</a></span></h3>

<p>liblc3 LC3 (Low Complexity Communication Codec) encoder wrapper.
</p>
<p>Requires the presence of the liblc3 headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-liblc3</code>.
</p>
<p>This encoder has support for the Bluetooth SIG LC3 codec for the LE Audio
protocol, and the following features of LC3plus:
</p><ul class="itemize mark-bullet">
<li>Frame duration of 2.5 and 5ms.
</li><li>High-Resolution mode, 48 KHz, and 96 kHz sampling rates.
</li></ul>

<p>For more information see the liblc3 project at
<a class="url" href="https://github.com/google/liblc3">https://github.com/google/liblc3</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-15" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-15">
<h4 class="subsection"><span>8.6.1 Options<a class="copiable-link" href="#Options-15"> &para;</a></span></h4>

<p>The following options are mapped on the shared FFmpeg codec options.
</p>
<dl class="table">
<dt><samp class="option">b <var class="var">bitrate</var></samp></dt>
<dd><p>Set the bit rate in bits/s. This will determine the fixed size of the encoded
frames, for a selected frame duration.
</p>
</dd>
<dt><samp class="option">ar <var class="var">frequency</var></samp></dt>
<dd><p>Set the audio sampling rate (in Hz).
</p>
</dd>
<dt><samp class="option">channels</samp></dt>
<dd><p>Set the number of audio channels.
</p>
</dd>
<dt><samp class="option">frame_duration</samp></dt>
<dd><p>Set the audio frame duration in milliseconds. Default value is 10ms.
Allowed frame durations are 2.5ms, 5ms, 7.5ms and 10ms.
LC3 (Bluetooth LE Audio), allows 7.5ms and 10ms; and LC3plus 2.5ms, 5ms
and 10ms.
</p>
<p>The 10ms frame duration is available in LC3 and LC3 plus standard.
In this mode, the produced bitstream can be referenced either as LC3 or LC3plus.
</p>
</dd>
<dt><samp class="option">high_resolution <var class="var">boolean</var></samp></dt>
<dd><p>Enable the high-resolution mode if set to 1. The high-resolution mode is
available with all LC3plus frame durations and for a sampling rate of 48 KHz,
and 96 KHz.
</p>
<p>The encoder automatically turns off this mode at lower sampling rates and
activates it at 96 KHz.
</p>
<p>This mode should be preferred at high bitrates. In this mode, the audio
bandwidth is always up to the Nyquist frequency, compared to LC3 at 48 KHz,
which limits the bandwidth to 20 KHz.
</p></dd>
</dl>

<a class="anchor" id="libmp3lame"></a></div>
</div>
<div class="section-level-extent" id="libmp3lame-1">
<h3 class="section"><span>8.7 libmp3lame<a class="copiable-link" href="#libmp3lame-1"> &para;</a></span></h3>

<p>LAME (Lame Ain&rsquo;t an MP3 Encoder) MP3 encoder wrapper.
</p>
<p>Requires the presence of the libmp3lame headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libmp3lame</code>.
</p>
<p>See <a class="ref" href="#libshine">libshine</a> for a fixed-point MP3 encoder, although with a
lower quality.
</p>
<ul class="mini-toc">
<li><a href="#Options-16" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-16">
<h4 class="subsection"><span>8.7.1 Options<a class="copiable-link" href="#Options-16"> &para;</a></span></h4>

<p>The following options are supported by the libmp3lame wrapper. The
<code class="command">lame</code>-equivalent of the options are listed in parentheses.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">-b</em>)</samp></dt>
<dd><p>Set bitrate expressed in bits/s for CBR or ABR. LAME <code class="code">bitrate</code> is
expressed in kilobits/s.
</p>
</dd>
<dt><samp class="option">q (<em class="emph">-V</em>)</samp></dt>
<dd><p>Set constant quality setting for VBR. This option is valid only
using the <code class="command">ffmpeg</code> command-line tool. For library interface
users, use <samp class="option">global_quality</samp>.
</p>
</dd>
<dt><samp class="option">compression_level (<em class="emph">-q</em>)</samp></dt>
<dd><p>Set algorithm quality. Valid arguments are integers in the 0-9 range,
with 0 meaning highest quality but slowest, and 9 meaning fastest
while producing the worst quality.
</p>
</dd>
<dt><samp class="option">cutoff (<em class="emph">--lowpass</em>)</samp></dt>
<dd><p>Set lowpass cutoff frequency. If unspecified, the encoder dynamically
adjusts the cutoff.
</p>
</dd>
<dt><samp class="option">reservoir</samp></dt>
<dd><p>Enable use of bit reservoir when set to 1. Default value is 1. LAME
has this enabled by default, but can be overridden by use
<samp class="option">--nores</samp> option.
</p>
</dd>
<dt><samp class="option">joint_stereo (<em class="emph">-m j</em>)</samp></dt>
<dd><p>Enable the encoder to use (on a frame by frame basis) either L/R
stereo or mid/side stereo. Default value is 1.
</p>
</dd>
<dt><samp class="option">abr (<em class="emph">--abr</em>)</samp></dt>
<dd><p>Enable the encoder to use ABR when set to 1. The <code class="command">lame</code>
<samp class="option">--abr</samp> sets the target bitrate, while this options only
tells FFmpeg to use ABR still relies on <samp class="option">b</samp> to set bitrate.
</p>
</dd>
<dt><samp class="option">copyright (<em class="emph">-c</em>)</samp></dt>
<dd><p>Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).
</p>
</dd>
<dt><samp class="option">original (<em class="emph">-o</em>)</samp></dt>
<dd><p>Set MPEG audio original flag when set to 1. The default value is 1
(enabled).
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libopencore_002damrnb-1">
<h3 class="section"><span>8.8 libopencore-amrnb<a class="copiable-link" href="#libopencore_002damrnb-1"> &para;</a></span></h3>

<p>OpenCORE Adaptive Multi-Rate Narrowband encoder.
</p>
<p>Requires the presence of the libopencore-amrnb headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libopencore-amrnb --enable-version3</code>.
</p>
<p>This is a mono-only encoder. Officially it only supports 8000Hz sample rate,
but you can override it by setting <samp class="option">strict</samp> to &lsquo;<samp class="samp">unofficial</samp>&rsquo; or
lower.
</p>
<ul class="mini-toc">
<li><a href="#Options-17" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-17">
<h4 class="subsection"><span>8.8.1 Options<a class="copiable-link" href="#Options-17"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bitrate in bits per second. Only the following bitrates are supported,
otherwise libavcodec will round to the nearest valid bitrate.
</p>
<dl class="table">
<dt><samp class="option">4750</samp></dt>
<dt><samp class="option">5150</samp></dt>
<dt><samp class="option">5900</samp></dt>
<dt><samp class="option">6700</samp></dt>
<dt><samp class="option">7400</samp></dt>
<dt><samp class="option">7950</samp></dt>
<dt><samp class="option">10200</samp></dt>
<dt><samp class="option">12200</samp></dt>
</dl>

</dd>
<dt><samp class="option">dtx</samp></dt>
<dd><p>Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libopus-1">
<h3 class="section"><span>8.9 libopus<a class="copiable-link" href="#libopus-1"> &para;</a></span></h3>

<p>libopus Opus Interactive Audio Codec encoder wrapper.
</p>
<p>Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libopus</code>.
</p>
<ul class="mini-toc">
<li><a href="#Option-Mapping" accesskey="1">Option Mapping</a></li>
</ul>
<div class="subsection-level-extent" id="Option-Mapping">
<h4 class="subsection"><span>8.9.1 Option Mapping<a class="copiable-link" href="#Option-Mapping"> &para;</a></span></h4>

<p>Most libopus options are modelled after the <code class="command">opusenc</code> utility from
opus-tools. The following is an option mapping chart describing options
supported by the libopus wrapper, and their <code class="command">opusenc</code>-equivalent
in parentheses.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">bitrate</em>)</samp></dt>
<dd><p>Set the bit rate in bits/s.  FFmpeg&rsquo;s <samp class="option">b</samp> option is
expressed in bits/s, while <code class="command">opusenc</code>&rsquo;s <samp class="option">bitrate</samp> in
kilobits/s.
</p>
</dd>
<dt><samp class="option">vbr (<em class="emph">vbr</em>, <em class="emph">hard-cbr</em>, and <em class="emph">cvbr</em>)</samp></dt>
<dd><p>Set VBR mode. The FFmpeg <samp class="option">vbr</samp> option has the following
valid arguments, with the <code class="command">opusenc</code> equivalent options
in parentheses:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">off (<em class="emph">hard-cbr</em>)</samp>&rsquo;</dt>
<dd><p>Use constant bit rate encoding.
</p>
</dd>
<dt>&lsquo;<samp class="samp">on (<em class="emph">vbr</em>)</samp>&rsquo;</dt>
<dd><p>Use variable bit rate encoding (the default).
</p>
</dd>
<dt>&lsquo;<samp class="samp">constrained (<em class="emph">cvbr</em>)</samp>&rsquo;</dt>
<dd><p>Use constrained variable bit rate encoding.
</p></dd>
</dl>

</dd>
<dt><samp class="option">compression_level (<em class="emph">comp</em>)</samp></dt>
<dd><p>Set encoding algorithm complexity. Valid options are integers in
the 0-10 range. 0 gives the fastest encodes but lower quality, while 10
gives the highest quality but slowest encoding. The default is 10.
</p>
</dd>
<dt><samp class="option">frame_duration (<em class="emph">framesize</em>)</samp></dt>
<dd><p>Set maximum frame size, or duration of a frame in milliseconds. The
argument must be exactly the following: 2.5, 5, 10, 20, 40, 60. Smaller
frame sizes achieve lower latency but less quality at a given bitrate.
Sizes greater than 20ms are only interesting at fairly low bitrates.
The default is 20ms.
</p>
</dd>
<dt><samp class="option">packet_loss (<em class="emph">expect-loss</em>)</samp></dt>
<dd><p>Set expected packet loss percentage. The default is 0.
</p>
</dd>
<dt><samp class="option">fec (<em class="emph">n/a</em>)</samp></dt>
<dd><p>Enable inband forward error correction. <samp class="option">packet_loss</samp> must be non-zero
to take advantage - frequency of FEC &rsquo;side-data&rsquo; is proportional to expected packet loss.
Default is disabled.
</p>
</dd>
<dt><samp class="option">application (N.A.)</samp></dt>
<dd><p>Set intended application type. Valid options are listed below:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">voip</samp>&rsquo;</dt>
<dd><p>Favor improved speech intelligibility.
</p></dd>
<dt>&lsquo;<samp class="samp">audio</samp>&rsquo;</dt>
<dd><p>Favor faithfulness to the input (the default).
</p></dd>
<dt>&lsquo;<samp class="samp">lowdelay</samp>&rsquo;</dt>
<dd><p>Restrict to only the lowest delay modes by disabling voice-optimized
modes.
</p></dd>
</dl>

</dd>
<dt><samp class="option">cutoff (N.A.)</samp></dt>
<dd><p>Set cutoff bandwidth in Hz. The argument must be exactly one of the
following: 4000, 6000, 8000, 12000, or 20000, corresponding to
narrowband, mediumband, wideband, super wideband, and fullband
respectively. The default is 0 (cutoff disabled). Note that libopus
forces a wideband cutoff for bitrates &lt; 15 kbps, unless CELT-only
(<samp class="option">application</samp> set to &lsquo;<samp class="samp">lowdelay</samp>&rsquo;) mode is used.
</p>
</dd>
<dt><samp class="option">mapping_family (<em class="emph">mapping_family</em>)</samp></dt>
<dd><p>Set channel mapping family to be used by the encoder. The default value of -1
uses mapping family 0 for mono and stereo inputs, and mapping family 1
otherwise. The default also disables the surround masking and LFE bandwidth
optimzations in libopus, and requires that the input contains 8 channels or
fewer.
</p>
<p>Other values include 0 for mono and stereo, 1 for surround sound with masking
and LFE bandwidth optimizations, and 255 for independent streams with an
unspecified channel layout.
</p>
</dd>
<dt><samp class="option">apply_phase_inv (N.A.) (requires libopus &gt;= 1.2)</samp></dt>
<dd><p>If set to 0, disables the use of phase inversion for intensity stereo,
improving the quality of mono downmixes, but slightly reducing normal stereo
quality. The default is 1 (phase inversion enabled).
</p>
</dd>
</dl>

<a class="anchor" id="libshine"></a></div>
</div>
<div class="section-level-extent" id="libshine-1">
<h3 class="section"><span>8.10 libshine<a class="copiable-link" href="#libshine-1"> &para;</a></span></h3>

<p>Shine Fixed-Point MP3 encoder wrapper.
</p>
<p>Shine is a fixed-point MP3 encoder. It has a far better performance on
platforms without an FPU, e.g. armel CPUs, and some phones and tablets.
However, as it is more targeted on performance than quality, it is not on par
with LAME and other production-grade encoders quality-wise. Also, according to
the project&rsquo;s homepage, this encoder may not be free of bugs as the code was
written a long time ago and the project was dead for at least 5 years.
</p>
<p>This encoder only supports stereo and mono input. This is also CBR-only.
</p>
<p>The original project (last updated in early 2007) is at
<a class="url" href="http://sourceforge.net/projects/libshine-fxp/">http://sourceforge.net/projects/libshine-fxp/</a>. We only support the
updated fork by the Savonet/Liquidsoap project at <a class="url" href="https://github.com/savonet/shine">https://github.com/savonet/shine</a>.
</p>
<p>Requires the presence of the libshine headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libshine</code>.
</p>
<p>See also <a class="ref" href="#libmp3lame">libmp3lame</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-18" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-18">
<h4 class="subsection"><span>8.10.1 Options<a class="copiable-link" href="#Options-18"> &para;</a></span></h4>

<p>The following options are supported by the libshine wrapper. The
<code class="command">shineenc</code>-equivalent of the options are listed in parentheses.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">-b</em>)</samp></dt>
<dd><p>Set bitrate expressed in bits/s for CBR. <code class="command">shineenc</code> <samp class="option">-b</samp> option
is expressed in kilobits/s.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libtwolame">
<h3 class="section"><span>8.11 libtwolame<a class="copiable-link" href="#libtwolame"> &para;</a></span></h3>

<p>TwoLAME MP2 encoder wrapper.
</p>
<p>Requires the presence of the libtwolame headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libtwolame</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-19" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-19">
<h4 class="subsection"><span>8.11.1 Options<a class="copiable-link" href="#Options-19"> &para;</a></span></h4>

<p>The following options are supported by the libtwolame wrapper. The
<code class="command">twolame</code>-equivalent options follow the FFmpeg ones and are in
parentheses.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">-b</em>)</samp></dt>
<dd><p>Set bitrate expressed in bits/s for CBR. <code class="command">twolame</code> <samp class="option">b</samp>
option is expressed in kilobits/s. Default value is 128k.
</p>
</dd>
<dt><samp class="option">q (<em class="emph">-V</em>)</samp></dt>
<dd><p>Set quality for experimental VBR support. Maximum value range is
from -50 to 50, useful range is from -10 to 10. The higher the
value, the better the quality. This option is valid only using the
<code class="command">ffmpeg</code> command-line tool. For library interface users,
use <samp class="option">global_quality</samp>.
</p>
</dd>
<dt><samp class="option">mode (<em class="emph">--mode</em>)</samp></dt>
<dd><p>Set the mode of the resulting audio. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>Choose mode automatically based on the input. This is the default.
</p></dd>
<dt>&lsquo;<samp class="samp">stereo</samp>&rsquo;</dt>
<dd><p>Stereo
</p></dd>
<dt>&lsquo;<samp class="samp">joint_stereo</samp>&rsquo;</dt>
<dd><p>Joint stereo
</p></dd>
<dt>&lsquo;<samp class="samp">dual_channel</samp>&rsquo;</dt>
<dd><p>Dual channel
</p></dd>
<dt>&lsquo;<samp class="samp">mono</samp>&rsquo;</dt>
<dd><p>Mono
</p></dd>
</dl>

</dd>
<dt><samp class="option">psymodel (<em class="emph">--psyc-mode</em>)</samp></dt>
<dd><p>Set psychoacoustic model to use in encoding. The argument must be
an integer between -1 and 4, inclusive. The higher the value, the
better the quality. The default value is 3.
</p>
</dd>
<dt><samp class="option">energy_levels (<em class="emph">--energy</em>)</samp></dt>
<dd><p>Enable energy levels extensions when set to 1. The default value is
0 (disabled).
</p>
</dd>
<dt><samp class="option">error_protection (<em class="emph">--protect</em>)</samp></dt>
<dd><p>Enable CRC error protection when set to 1. The default value is 0
(disabled).
</p>
</dd>
<dt><samp class="option">copyright (<em class="emph">--copyright</em>)</samp></dt>
<dd><p>Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).
</p>
</dd>
<dt><samp class="option">original (<em class="emph">--original</em>)</samp></dt>
<dd><p>Set MPEG audio original flag when set to 1. The default value is 0
(disabled).
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libvo_002damrwbenc">
<h3 class="section"><span>8.12 libvo-amrwbenc<a class="copiable-link" href="#libvo_002damrwbenc"> &para;</a></span></h3>

<p>VisualOn Adaptive Multi-Rate Wideband encoder.
</p>
<p>Requires the presence of the libvo-amrwbenc headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libvo-amrwbenc --enable-version3</code>.
</p>
<p>This is a mono-only encoder. Officially it only supports 16000Hz sample
rate, but you can override it by setting <samp class="option">strict</samp> to
&lsquo;<samp class="samp">unofficial</samp>&rsquo; or lower.
</p>
<ul class="mini-toc">
<li><a href="#Options-20" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-20">
<h4 class="subsection"><span>8.12.1 Options<a class="copiable-link" href="#Options-20"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bitrate in bits/s. Only the following bitrates are supported, otherwise
libavcodec will round to the nearest valid bitrate.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">6600</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">8850</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">12650</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">14250</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">15850</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">18250</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">19850</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">23050</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">23850</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">dtx</samp></dt>
<dd><p>Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libvorbis">
<h3 class="section"><span>8.13 libvorbis<a class="copiable-link" href="#libvorbis"> &para;</a></span></h3>

<p>libvorbis encoder wrapper.
</p>
<p>Requires the presence of the libvorbisenc headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libvorbis</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-21" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-21">
<h4 class="subsection"><span>8.13.1 Options<a class="copiable-link" href="#Options-21"> &para;</a></span></h4>

<p>The following options are supported by the libvorbis wrapper. The
<code class="command">oggenc</code>-equivalent of the options are listed in parentheses.
</p>
<p>To get a more accurate and extensive documentation of the libvorbis
options, consult the libvorbisenc&rsquo;s and <code class="command">oggenc</code>&rsquo;s documentations.
See <a class="url" href="http://xiph.org/vorbis/">http://xiph.org/vorbis/</a>,
<a class="url" href="http://wiki.xiph.org/Vorbis-tools">http://wiki.xiph.org/Vorbis-tools</a>, and oggenc(1).
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">-b</em>)</samp></dt>
<dd><p>Set bitrate expressed in bits/s for ABR. <code class="command">oggenc</code> <samp class="option">-b</samp> is
expressed in kilobits/s.
</p>
</dd>
<dt><samp class="option">q (<em class="emph">-q</em>)</samp></dt>
<dd><p>Set constant quality setting for VBR. The value should be a float
number in the range of -1.0 to 10.0. The higher the value, the better
the quality. The default value is &lsquo;<samp class="samp">3.0</samp>&rsquo;.
</p>
<p>This option is valid only using the <code class="command">ffmpeg</code> command-line tool.
For library interface users, use <samp class="option">global_quality</samp>.
</p>
</dd>
<dt><samp class="option">cutoff (<em class="emph">--advanced-encode-option lowpass_frequency=N</em>)</samp></dt>
<dd><p>Set cutoff bandwidth in Hz, a value of 0 disables cutoff. <code class="command">oggenc</code>&rsquo;s
related option is expressed in kHz. The default value is &lsquo;<samp class="samp">0</samp>&rsquo; (cutoff
disabled).
</p>
</dd>
<dt><samp class="option">minrate (<em class="emph">-m</em>)</samp></dt>
<dd><p>Set minimum bitrate expressed in bits/s. <code class="command">oggenc</code> <samp class="option">-m</samp> is
expressed in kilobits/s.
</p>
</dd>
<dt><samp class="option">maxrate (<em class="emph">-M</em>)</samp></dt>
<dd><p>Set maximum bitrate expressed in bits/s. <code class="command">oggenc</code> <samp class="option">-M</samp> is
expressed in kilobits/s. This only has effect on ABR mode.
</p>
</dd>
<dt><samp class="option">iblock (<em class="emph">--advanced-encode-option impulse_noisetune=N</em>)</samp></dt>
<dd><p>Set noise floor bias for impulse blocks. The value is a float number from
-15.0 to 0.0. A negative bias instructs the encoder to pay special attention
to the crispness of transients in the encoded audio. The tradeoff for better
transient response is a higher bitrate.
</p>
</dd>
</dl>

<a class="anchor" id="mjpegenc"></a></div>
</div>
<div class="section-level-extent" id="mjpeg">
<h3 class="section"><span>8.14 mjpeg<a class="copiable-link" href="#mjpeg"> &para;</a></span></h3>

<p>Motion JPEG encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-22" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-22">
<h4 class="subsection"><span>8.14.1 Options<a class="copiable-link" href="#Options-22"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">huffman</samp></dt>
<dd><p>Set the huffman encoding strategy. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dd><p>Use the default huffman tables. This is the default strategy.
</p>
</dd>
<dt>&lsquo;<samp class="samp">optimal</samp>&rsquo;</dt>
<dd><p>Compute and use optimal huffman tables.
</p>
</dd>
</dl>
</dd>
</dl>

<a class="anchor" id="wavpackenc"></a></div>
</div>
<div class="section-level-extent" id="wavpack">
<h3 class="section"><span>8.15 wavpack<a class="copiable-link" href="#wavpack"> &para;</a></span></h3>

<p>WavPack lossless audio encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-23" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-23">
<h4 class="subsection"><span>8.15.1 Options<a class="copiable-link" href="#Options-23"> &para;</a></span></h4>

<p>The equivalent options for <code class="command">wavpack</code> command line utility are listed in
parentheses.
</p>
<ul class="mini-toc">
<li><a href="#Shared-options" accesskey="1">Shared options</a></li>
<li><a href="#Private-options" accesskey="2">Private options</a></li>
</ul>
<div class="subsubsection-level-extent" id="Shared-options">
<h4 class="subsubsection"><span>8.15.1.1 Shared options<a class="copiable-link" href="#Shared-options"> &para;</a></span></h4>

<p>The following shared options are effective for this encoder. Only special notes
about this particular encoder will be documented here. For the general meaning
of the options, see <a class="ref" href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<dl class="table">
<dt><samp class="option">frame_size (<em class="emph">--blocksize</em>)</samp></dt>
<dd><p>For this encoder, the range for this option is between 128 and 131072. Default
is automatically decided based on sample rate and number of channel.
</p>
<p>For the complete formula of calculating default, see
<samp class="file">libavcodec/wavpackenc.c</samp>.
</p>
</dd>
<dt><samp class="option">compression_level (<em class="emph">-f</em>, <em class="emph">-h</em>, <em class="emph">-hh</em>, and <em class="emph">-x</em>)</samp></dt>
</dl>

</div>
<div class="subsubsection-level-extent" id="Private-options">
<h4 class="subsubsection"><span>8.15.1.2 Private options<a class="copiable-link" href="#Private-options"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">joint_stereo (<em class="emph">-j</em>)</samp></dt>
<dd><p>Set whether to enable joint stereo. Valid values are:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">on (<em class="emph">1</em>)</samp>&rsquo;</dt>
<dd><p>Force mid/side audio encoding.
</p></dd>
<dt>&lsquo;<samp class="samp">off (<em class="emph">0</em>)</samp>&rsquo;</dt>
<dd><p>Force left/right audio encoding.
</p></dd>
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dd><p>Let the encoder decide automatically.
</p></dd>
</dl>

</dd>
<dt><samp class="option">optimize_mono</samp></dt>
<dd><p>Set whether to enable optimization for mono. This option is only effective for
non-mono streams. Available values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">on</samp>&rsquo;</dt>
<dd><p>enabled
</p></dd>
<dt>&lsquo;<samp class="samp">off</samp>&rsquo;</dt>
<dd><p>disabled
</p></dd>
</dl>

</dd>
</dl>


</div>
</div>
</div>
</div>
<div class="chapter-level-extent" id="Video-Encoders">
<h2 class="chapter"><span>9 Video Encoders<a class="copiable-link" href="#Video-Encoders"> &para;</a></span></h2>

<p>A description of some of the currently available video encoders
follows.
</p>
<ul class="mini-toc">
<li><a href="#a64_005fmulti_002c-a64_005fmulti5" accesskey="1">a64_multi, a64_multi5</a></li>
<li><a href="#Cinepak" accesskey="2">Cinepak</a></li>
<li><a href="#GIF" accesskey="3">GIF</a></li>
<li><a href="#Hap" accesskey="4">Hap</a></li>
<li><a href="#jpeg2000" accesskey="5">jpeg2000</a></li>
<li><a href="#librav1e" accesskey="6">librav1e</a></li>
<li><a href="#libaom_002dav1" accesskey="7">libaom-av1</a></li>
<li><a href="#libsvtav1" accesskey="8">libsvtav1</a></li>
<li><a href="#libjxl" accesskey="9">libjxl</a></li>
<li><a href="#libkvazaar">libkvazaar</a></li>
<li><a href="#libopenh264">libopenh264</a></li>
<li><a href="#libtheora">libtheora</a></li>
<li><a href="#libvpx">libvpx</a></li>
<li><a href="#libvvenc">libvvenc</a></li>
<li><a href="#libwebp">libwebp</a></li>
<li><a href="#libx264_002c-libx264rgb">libx264, libx264rgb</a></li>
<li><a href="#libx265">libx265</a></li>
<li><a href="#libxavs2">libxavs2</a></li>
<li><a href="#libxeve">libxeve</a></li>
<li><a href="#libxvid">libxvid</a></li>
<li><a href="#MediaFoundation">MediaFoundation</a></li>
<li><a href="#Microsoft-RLE">Microsoft RLE</a></li>
<li><a href="#mpeg2">mpeg2</a></li>
<li><a href="#png">png</a></li>
<li><a href="#ProRes">ProRes</a></li>
<li><a href="#QSV-Encoders">QSV Encoders</a></li>
<li><a href="#snow">snow</a></li>
<li><a href="#VAAPI-encoders">VAAPI encoders</a></li>
<li><a href="#vbn">vbn</a></li>
<li><a href="#vc2">vc2</a></li>
</ul>
<div class="section-level-extent" id="a64_005fmulti_002c-a64_005fmulti5">
<h3 class="section"><span>9.1 a64_multi, a64_multi5<a class="copiable-link" href="#a64_005fmulti_002c-a64_005fmulti5"> &para;</a></span></h3>

<p>A64 / Commodore 64 multicolor charset encoder. <code class="code">a64_multi5</code> is extended with 5th color (colram).
</p>
</div>
<div class="section-level-extent" id="Cinepak">
<h3 class="section"><span>9.2 Cinepak<a class="copiable-link" href="#Cinepak"> &para;</a></span></h3>

<p>Cinepak aka CVID encoder.
Compatible with Windows 3.1 and vintage MacOS.
</p>
<ul class="mini-toc">
<li><a href="#Options-24" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-24">
<h4 class="subsection"><span>9.2.1 Options<a class="copiable-link" href="#Options-24"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">g <var class="var">integer</var></samp></dt>
<dd><p>Keyframe interval.
A keyframe is inserted at least every <code class="code">-g</code> frames, sometimes sooner.
</p>
</dd>
<dt><samp class="option">q:v <var class="var">integer</var></samp></dt>
<dd><p>Quality factor. Lower is better. Higher gives lower bitrate.
The following table lists bitrates when encoding akiyo_cif.y4m for various values of <code class="code">-q:v</code> with <code class="code">-g 100</code>:
</p>
<dl class="table">
<dt><samp class="option"><code class="code">-q:v 1</code> 1918 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 2</code> 1735 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 4</code> 1500 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 10</code> 1041 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 20</code> 826 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 40</code> 553 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 100</code> 394 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 200</code> 312 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 400</code> 266 kb/s</samp></dt>
<dt><samp class="option"><code class="code">-q:v 1000</code> 237 kb/s</samp></dt>
</dl>

</dd>
<dt><samp class="option">max_extra_cb_iterations <var class="var">integer</var></samp></dt>
<dd><p>Max extra codebook recalculation passes, more is better and slower.
</p>
</dd>
<dt><samp class="option">skip_empty_cb <var class="var">boolean</var></samp></dt>
<dd><p>Avoid wasting bytes, ignore vintage MacOS decoder.
</p>
</dd>
<dt><samp class="option">max_strips <var class="var">integer</var></samp></dt>
<dt><samp class="option">min_strips <var class="var">integer</var></samp></dt>
<dd><p>The minimum and maximum number of strips to use.
Wider range sometimes improves quality.
More strips is generally better quality but costs more bits.
Fewer strips tend to yield more keyframes.
Vintage compatible is 1..3.
</p>
</dd>
<dt><samp class="option">strip_number_adaptivity <var class="var">integer</var></samp></dt>
<dd><p>How much number of strips is allowed to change between frames.
Higher is better but slower.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="GIF">
<h3 class="section"><span>9.3 GIF<a class="copiable-link" href="#GIF"> &para;</a></span></h3>

<p>GIF image/animation encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-25" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-25">
<h4 class="subsection"><span>9.3.1 Options<a class="copiable-link" href="#Options-25"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">gifflags <var class="var">integer</var></samp></dt>
<dd><p>Sets the flags used for GIF encoding.
</p>
<dl class="table">
<dt><samp class="option">offsetting</samp></dt>
<dd><p>Enables picture offsetting.
</p>
<p>Default is enabled.
</p>
</dd>
<dt><samp class="option">transdiff</samp></dt>
<dd><p>Enables transparency detection between frames.
</p>
<p>Default is enabled.
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">gifimage <var class="var">integer</var></samp></dt>
<dd><p>Enables encoding one full GIF image per frame, rather than an animated GIF.
</p>
<p>Default value is <samp class="option">0</samp>.
</p>
</dd>
<dt><samp class="option">global_palette <var class="var">integer</var></samp></dt>
<dd><p>Writes a palette to the global GIF header where feasible.
</p>
<p>If disabled, every frame will always have a palette written, even if there
is a global palette supplied.
</p>
<p>Default value is <samp class="option">1</samp>.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="Hap">
<h3 class="section"><span>9.4 Hap<a class="copiable-link" href="#Hap"> &para;</a></span></h3>

<p>Vidvox Hap video encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-26" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-26">
<h4 class="subsection"><span>9.4.1 Options<a class="copiable-link" href="#Options-26"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">format <var class="var">integer</var></samp></dt>
<dd><p>Specifies the Hap format to encode.
</p>
<dl class="table">
<dt><samp class="option">hap</samp></dt>
<dt><samp class="option">hap_alpha</samp></dt>
<dt><samp class="option">hap_q</samp></dt>
</dl>

<p>Default value is <samp class="option">hap</samp>.
</p>
</dd>
<dt><samp class="option">chunks <var class="var">integer</var></samp></dt>
<dd><p>Specifies the number of chunks to split frames into, between 1 and 64. This
permits multithreaded decoding of large frames, potentially at the cost of
data-rate. The encoder may modify this value to divide frames evenly.
</p>
<p>Default value is <var class="var">1</var>.
</p>
</dd>
<dt><samp class="option">compressor <var class="var">integer</var></samp></dt>
<dd><p>Specifies the second-stage compressor to use. If set to <samp class="option">none</samp>,
<samp class="option">chunks</samp> will be limited to 1, as chunked uncompressed frames offer no
benefit.
</p>
<dl class="table">
<dt><samp class="option">none</samp></dt>
<dt><samp class="option">snappy</samp></dt>
</dl>

<p>Default value is <samp class="option">snappy</samp>.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="jpeg2000">
<h3 class="section"><span>9.5 jpeg2000<a class="copiable-link" href="#jpeg2000"> &para;</a></span></h3>

<p>The native jpeg 2000 encoder is lossy by default, the <code class="code">-q:v</code>
option can be used to set the encoding quality. Lossless encoding
can be selected with <code class="code">-pred 1</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-27" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-27">
<h4 class="subsection"><span>9.5.1 Options<a class="copiable-link" href="#Options-27"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">format <var class="var">integer</var></samp></dt>
<dd><p>Can be set to either <code class="code">j2k</code> or <code class="code">jp2</code> (the default) that
makes it possible to store non-rgb pix_fmts.
</p>
</dd>
<dt><samp class="option">tile_width <var class="var">integer</var></samp></dt>
<dd><p>Sets tile width. Range is 1 to 1073741824. Default is 256.
</p>
</dd>
<dt><samp class="option">tile_height <var class="var">integer</var></samp></dt>
<dd><p>Sets tile height. Range is 1 to 1073741824. Default is 256.
</p>
</dd>
<dt><samp class="option">pred <var class="var">integer</var></samp></dt>
<dd><p>Allows setting the discrete wavelet transform (DWT) type
</p><dl class="table">
<dt><samp class="option">dwt97int (Lossy)</samp></dt>
<dt><samp class="option">dwt53 (Lossless)</samp></dt>
</dl>
<p>Default is <code class="code">dwt97int</code>
</p>
</dd>
<dt><samp class="option">sop <var class="var">boolean</var></samp></dt>
<dd><p>Enable this to add SOP marker at the start of each packet. Disabled by default.
</p>
</dd>
<dt><samp class="option">eph <var class="var">boolean</var></samp></dt>
<dd><p>Enable this to add EPH marker at the end of each packet header. Disabled by default.
</p>
</dd>
<dt><samp class="option">prog <var class="var">integer</var></samp></dt>
<dd><p>Sets the progression order to be used by the encoder.
Possible values are:
</p><dl class="table">
<dt><samp class="option">lrcp</samp></dt>
<dt><samp class="option">rlcp</samp></dt>
<dt><samp class="option">rpcl</samp></dt>
<dt><samp class="option">pcrl</samp></dt>
<dt><samp class="option">cprl</samp></dt>
</dl>
<p>Set to <code class="code">lrcp</code> by default.
</p>
</dd>
<dt><samp class="option">layer_rates <var class="var">string</var></samp></dt>
<dd><p>By default, when this option is not used, compression is done using the quality metric.
This option allows for compression using compression ratio. The compression ratio for each
level could be specified. The compression ratio of a layer <code class="code">l</code> species the what ratio of
total file size is contained in the first <code class="code">l</code> layers.
</p>
<p>Example usage:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input.bmp -c:v jpeg2000 -layer_rates &quot;100,10,1&quot; output.j2k
</pre></div>

<p>This would compress the image to contain 3 layers, where the data contained in the
first layer would be compressed by 1000 times, compressed by 100 in the first two layers,
and shall contain all data while using all 3 layers.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="librav1e">
<h3 class="section"><span>9.6 librav1e<a class="copiable-link" href="#librav1e"> &para;</a></span></h3>

<p>rav1e AV1 encoder wrapper.
</p>
<p>Requires the presence of the rav1e headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-librav1e</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-28" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-28">
<h4 class="subsection"><span>9.6.1 Options<a class="copiable-link" href="#Options-28"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">qmax</samp></dt>
<dd><p>Sets the maximum quantizer to use when using bitrate mode.
</p>
</dd>
<dt><samp class="option">qmin</samp></dt>
<dd><p>Sets the minimum quantizer to use when using bitrate mode.
</p>
</dd>
<dt><samp class="option">qp</samp></dt>
<dd><p>Uses quantizer mode to encode at the given quantizer (0-255).
</p>
</dd>
<dt><samp class="option">speed</samp></dt>
<dd><p>Selects the speed preset (0-10) to encode with.
</p>
</dd>
<dt><samp class="option">tiles</samp></dt>
<dd><p>Selects how many tiles to encode with.
</p>
</dd>
<dt><samp class="option">tile-rows</samp></dt>
<dd><p>Selects how many rows of tiles to encode with.
</p>
</dd>
<dt><samp class="option">tile-columns</samp></dt>
<dd><p>Selects how many columns of tiles to encode with.
</p>
</dd>
<dt><samp class="option">rav1e-params</samp></dt>
<dd><p>Set rav1e options using a list of <var class="var">key</var>=<var class="var">value</var> pairs separated
by &quot;:&quot;. See <code class="command">rav1e --help</code> for a list of options.
</p>
<p>For example to specify librav1e encoding options with <samp class="option">-rav1e-params</samp>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input -c:v librav1e -b:v 500K -rav1e-params speed=5:low_latency=true output.mp4
</pre></div>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libaom_002dav1">
<h3 class="section"><span>9.7 libaom-av1<a class="copiable-link" href="#libaom_002dav1"> &para;</a></span></h3>

<p>libaom AV1 encoder wrapper.
</p>
<p>Requires the presence of the libaom headers and library during
configuration.  You need to explicitly configure the build with
<code class="code">--enable-libaom</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-29" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-29">
<h4 class="subsection"><span>9.7.1 Options<a class="copiable-link" href="#Options-29"> &para;</a></span></h4>

<p>The wrapper supports the following standard libavcodec options:
</p>
<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set bitrate target in bits/second.  By default this will use
variable-bitrate mode.  If <samp class="option">maxrate</samp> and <samp class="option">minrate</samp> are
also set to the same value then it will use constant-bitrate mode,
otherwise if <samp class="option">crf</samp> is set as well then it will use
constrained-quality mode.
</p>
</dd>
<dt><samp class="option">g keyint_min</samp></dt>
<dd><p>Set key frame placement.  The GOP size sets the maximum distance between
key frames; if zero the output stream will be intra-only.  The minimum
distance is ignored unless it is the same as the GOP size, in which case
key frames will always appear at a fixed interval.  Not set by default,
so without this option the library has completely free choice about
where to place key frames.
</p>
</dd>
<dt><samp class="option">qmin qmax</samp></dt>
<dd><p>Set minimum/maximum quantisation values.  Valid range is from 0 to 63
(warning: this does not match the quantiser values actually used by AV1
- divide by four to map real quantiser values to this range).  Defaults
to min/max (no constraint).
</p>
</dd>
<dt><samp class="option">minrate maxrate bufsize rc_init_occupancy</samp></dt>
<dd><p>Set rate control buffering parameters.  Not used if not set - defaults
to unconstrained variable bitrate.
</p>
</dd>
<dt><samp class="option">threads</samp></dt>
<dd><p>Set the number of threads to use while encoding.  This may require the
<samp class="option">tiles</samp> or <samp class="option">row-mt</samp> options to also be set to actually
use the specified number of threads fully. Defaults to the number of
hardware threads supported by the host machine.
</p>
</dd>
<dt><samp class="option">profile</samp></dt>
<dd><p>Set the encoding profile.  Defaults to using the profile which matches
the bit depth and chroma subsampling of the input.
</p>
</dd>
</dl>

<p>The wrapper also has some specific options:
</p>
<dl class="table">
<dt><samp class="option">cpu-used</samp></dt>
<dd><p>Set the quality/encoding speed tradeoff.  Valid range is from 0 to 8,
higher numbers indicating greater speed and lower quality.  The default
value is 1, which will be slow and high quality.
</p>
</dd>
<dt><samp class="option">auto-alt-ref</samp></dt>
<dd><p>Enable use of alternate reference frames.  Defaults to the internal
default of the library.
</p>
</dd>
<dt><samp class="option">arnr-max-frames (<em class="emph">frames</em>)</samp></dt>
<dd><p>Set altref noise reduction max frame count. Default is -1.
</p>
</dd>
<dt><samp class="option">arnr-strength (<em class="emph">strength</em>)</samp></dt>
<dd><p>Set altref noise reduction filter strength. Range is -1 to 6. Default is -1.
</p>
</dd>
<dt><samp class="option">aq-mode (<em class="emph">aq-mode</em>)</samp></dt>
<dd><p>Set adaptive quantization mode. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">0</em>)</samp>&rsquo;</dt>
<dd><p>Disabled.
</p>
</dd>
<dt>&lsquo;<samp class="samp">variance (<em class="emph">1</em>)</samp>&rsquo;</dt>
<dd><p>Variance-based.
</p>
</dd>
<dt>&lsquo;<samp class="samp">complexity (<em class="emph">2</em>)</samp>&rsquo;</dt>
<dd><p>Complexity-based.
</p>
</dd>
<dt>&lsquo;<samp class="samp">cyclic (<em class="emph">3</em>)</samp>&rsquo;</dt>
<dd><p>Cyclic refresh.
</p></dd>
</dl>

</dd>
<dt><samp class="option">tune (<em class="emph">tune</em>)</samp></dt>
<dd><p>Set the distortion metric the encoder is tuned with. Default is <code class="code">psnr</code>.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">psnr (<em class="emph">0</em>)</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">ssim (<em class="emph">1</em>)</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">lag-in-frames</samp></dt>
<dd><p>Set the maximum number of frames which the encoder may keep in flight
at any one time for lookahead purposes.  Defaults to the internal
default of the library.
</p>
</dd>
<dt><samp class="option">error-resilience</samp></dt>
<dd><p>Enable error resilience features:
</p><dl class="table">
<dt><samp class="option">default</samp></dt>
<dd><p>Improve resilience against losses of whole frames.
</p></dd>
</dl>
<p>Not enabled by default.
</p>
</dd>
<dt><samp class="option">crf</samp></dt>
<dd><p>Set the quality/size tradeoff for constant-quality (no bitrate target)
and constrained-quality (with maximum bitrate target) modes. Valid
range is 0 to 63, higher numbers indicating lower quality and smaller
output size.  Only used if set; by default only the bitrate target is
used.
</p>
</dd>
<dt><samp class="option">static-thresh</samp></dt>
<dd><p>Set a change threshold on blocks below which they will be skipped by
the encoder.  Defined in arbitrary units as a nonnegative integer,
defaulting to zero (no blocks are skipped).
</p>
</dd>
<dt><samp class="option">drop-threshold</samp></dt>
<dd><p>Set a threshold for dropping frames when close to rate control bounds.
Defined as a percentage of the target buffer - when the rate control
buffer falls below this percentage, frames will be dropped until it
has refilled above the threshold.  Defaults to zero (no frames are
dropped).
</p>
</dd>
<dt><samp class="option">denoise-noise-level (<em class="emph">level</em>)</samp></dt>
<dd><p>Amount of noise to be removed for grain synthesis. Grain synthesis is disabled if
this option is not set or set to 0.
</p>
</dd>
<dt><samp class="option">denoise-block-size (<em class="emph">pixels</em>)</samp></dt>
<dd><p>Block size used for denoising for grain synthesis. If not set, AV1 codec
uses the default value of 32.
</p>
</dd>
<dt><samp class="option">undershoot-pct (<em class="emph">pct</em>)</samp></dt>
<dd><p>Set datarate undershoot (min) percentage of the target bitrate. Range is -1 to 100.
Default is -1.
</p>
</dd>
<dt><samp class="option">overshoot-pct (<em class="emph">pct</em>)</samp></dt>
<dd><p>Set datarate overshoot (max) percentage of the target bitrate. Range is -1 to 1000.
Default is -1.
</p>
</dd>
<dt><samp class="option">minsection-pct (<em class="emph">pct</em>)</samp></dt>
<dd><p>Minimum percentage variation of the GOP bitrate from the target bitrate. If minsection-pct
is not set, the libaomenc wrapper computes it as follows: <code class="code">(minrate * 100 / bitrate)</code>.
Range is -1 to 100. Default is -1 (unset).
</p>
</dd>
<dt><samp class="option">maxsection-pct (<em class="emph">pct</em>)</samp></dt>
<dd><p>Maximum percentage variation of the GOP bitrate from the target bitrate. If maxsection-pct
is not set, the libaomenc wrapper computes it as follows: <code class="code">(maxrate * 100 / bitrate)</code>.
Range is -1 to 5000. Default is -1 (unset).
</p>
</dd>
<dt><samp class="option">frame-parallel (<em class="emph">boolean</em>)</samp></dt>
<dd><p>Enable frame parallel decodability features. Default is true.
</p>
</dd>
<dt><samp class="option">tiles</samp></dt>
<dd><p>Set the number of tiles to encode the input video with, as columns x
rows.  Larger numbers allow greater parallelism in both encoding and
decoding, but may decrease coding efficiency.  Defaults to the minimum
number of tiles required by the size of the input video (this is 1x1
(that is, a single tile) for sizes up to and including 4K).
</p>
</dd>
<dt><samp class="option">tile-columns tile-rows</samp></dt>
<dd><p>Set the number of tiles as log2 of the number of tile rows and columns.
Provided for compatibility with libvpx/VP9.
</p>
</dd>
<dt><samp class="option">row-mt (Requires libaom &gt;= 1.0.0-759-g90a15f4f2)</samp></dt>
<dd><p>Enable row based multi-threading. Disabled by default.
</p>
</dd>
<dt><samp class="option">enable-cdef (<em class="emph">boolean</em>)</samp></dt>
<dd><p>Enable Constrained Directional Enhancement Filter. The libaom-av1
encoder enables CDEF by default.
</p>
</dd>
<dt><samp class="option">enable-restoration (<em class="emph">boolean</em>)</samp></dt>
<dd><p>Enable Loop Restoration Filter. Default is true for libaom-av1.
</p>
</dd>
<dt><samp class="option">enable-global-motion (<em class="emph">boolean</em>)</samp></dt>
<dd><p>Enable the use of global motion for block prediction. Default is true.
</p>
</dd>
<dt><samp class="option">enable-intrabc (<em class="emph">boolean</em>)</samp></dt>
<dd><p>Enable block copy mode for intra block prediction. This mode is
useful for screen content. Default is true.
</p>
</dd>
<dt><samp class="option">enable-rect-partitions (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable rectangular partitions. Default is true.
</p>
</dd>
<dt><samp class="option">enable-1to4-partitions (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable 1:4/4:1 partitions. Default is true.
</p>
</dd>
<dt><samp class="option">enable-ab-partitions (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable AB shape partitions. Default is true.
</p>
</dd>
<dt><samp class="option">enable-angle-delta (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable angle delta intra prediction. Default is true.
</p>
</dd>
<dt><samp class="option">enable-cfl-intra (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable chroma predicted from luma intra prediction. Default is true.
</p>
</dd>
<dt><samp class="option">enable-filter-intra (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable filter intra predictor. Default is true.
</p>
</dd>
<dt><samp class="option">enable-intra-edge-filter (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable intra edge filter. Default is true.
</p>
</dd>
<dt><samp class="option">enable-smooth-intra (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable smooth intra prediction mode. Default is true.
</p>
</dd>
<dt><samp class="option">enable-paeth-intra (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable paeth predictor in intra prediction. Default is true.
</p>
</dd>
<dt><samp class="option">enable-palette (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable palette prediction mode. Default is true.
</p>
</dd>
<dt><samp class="option">enable-flip-idtx (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable extended transform type, including FLIPADST_DCT, DCT_FLIPADST,
FLIPADST_FLIPADST, ADST_FLIPADST, FLIPADST_ADST, IDTX, V_DCT, H_DCT,
V_ADST, H_ADST, V_FLIPADST, H_FLIPADST. Default is true.
</p>
</dd>
<dt><samp class="option">enable-tx64 (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable 64-pt transform. Default is true.
</p>
</dd>
<dt><samp class="option">reduced-tx-type-set (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Use reduced set of transform types. Default is false.
</p>
</dd>
<dt><samp class="option">use-intra-dct-only (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Use DCT only for INTRA modes. Default is false.
</p>
</dd>
<dt><samp class="option">use-inter-dct-only (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Use DCT only for INTER modes. Default is false.
</p>
</dd>
<dt><samp class="option">use-intra-default-tx-only (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Use Default-transform only for INTRA modes. Default is false.
</p>
</dd>
<dt><samp class="option">enable-ref-frame-mvs (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable temporal mv prediction. Default is true.
</p>
</dd>
<dt><samp class="option">enable-reduced-reference-set (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Use reduced set of single and compound references. Default is false.
</p>
</dd>
<dt><samp class="option">enable-obmc (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable obmc. Default is true.
</p>
</dd>
<dt><samp class="option">enable-dual-filter (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable dual filter. Default is true.
</p>
</dd>
<dt><samp class="option">enable-diff-wtd-comp (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable difference-weighted compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-dist-wtd-comp (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable distance-weighted compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-onesided-comp (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable one sided compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-interinter-wedge (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable interinter wedge compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-interintra-wedge (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable interintra wedge compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-masked-comp (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable masked compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-interintra-comp (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable interintra compound. Default is true.
</p>
</dd>
<dt><samp class="option">enable-smooth-interintra (<em class="emph">boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></dt>
<dd><p>Enable smooth interintra mode. Default is true.
</p>
</dd>
<dt><samp class="option">aom-params</samp></dt>
<dd><p>Set libaom options using a list of <var class="var">key</var>=<var class="var">value</var> pairs separated
by &quot;:&quot;. For a list of supported options, see <code class="command">aomenc --help</code> under the
section &quot;AV1 Specific Options&quot;.
</p>
<p>For example to specify libaom encoding options with <samp class="option">-aom-params</samp>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input -c:v libaom-av1 -b:v 500K -aom-params tune=psnr:enable-tpl-model=1 output.mp4
</pre></div>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libsvtav1">
<h3 class="section"><span>9.8 libsvtav1<a class="copiable-link" href="#libsvtav1"> &para;</a></span></h3>

<p>SVT-AV1 encoder wrapper.
</p>
<p>Requires the presence of the SVT-AV1 headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-libsvtav1</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-30" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-30">
<h4 class="subsection"><span>9.8.1 Options<a class="copiable-link" href="#Options-30"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">profile</samp></dt>
<dd><p>Set the encoding profile.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">professional</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">level</samp></dt>
<dd><p>Set the operating point level. For example: &rsquo;4.0&rsquo;
</p>
</dd>
<dt><samp class="option">hielevel</samp></dt>
<dd><p>Set the Hierarchical prediction levels.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">3level</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">4level</samp>&rsquo;</dt>
<dd><p>This is the default.
</p></dd>
</dl>

</dd>
<dt><samp class="option">tier</samp></dt>
<dd><p>Set the operating point tier.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dd><p>This is the default.
</p></dd>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">qmax</samp></dt>
<dd><p>Set the maximum quantizer to use when using a bitrate mode.
</p>
</dd>
<dt><samp class="option">qmin</samp></dt>
<dd><p>Set the minimum quantizer to use when using a bitrate mode.
</p>
</dd>
<dt><samp class="option">crf</samp></dt>
<dd><p>Constant rate factor value used in crf rate control mode (0-63).
</p>
</dd>
<dt><samp class="option">qp</samp></dt>
<dd><p>Set the quantizer used in cqp rate control mode (0-63).
</p>
</dd>
<dt><samp class="option">sc_detection</samp></dt>
<dd><p>Enable scene change detection.
</p>
</dd>
<dt><samp class="option">la_depth</samp></dt>
<dd><p>Set number of frames to look ahead (0-120).
</p>
</dd>
<dt><samp class="option">preset</samp></dt>
<dd><p>Set the quality-speed tradeoff, in the range 0 to 13.  Higher values are
faster but lower quality.
</p>
</dd>
<dt><samp class="option">tile_rows</samp></dt>
<dd><p>Set log2 of the number of rows of tiles to use (0-6).
</p>
</dd>
<dt><samp class="option">tile_columns</samp></dt>
<dd><p>Set log2 of the number of columns of tiles to use (0-4).
</p>
</dd>
<dt><samp class="option">svtav1-params</samp></dt>
<dd><p>Set SVT-AV1 options using a list of <var class="var">key</var>=<var class="var">value</var> pairs separated
by &quot;:&quot;. See the SVT-AV1 encoder user guide for a list of accepted parameters.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libjxl">
<h3 class="section"><span>9.9 libjxl<a class="copiable-link" href="#libjxl"> &para;</a></span></h3>

<p>libjxl JPEG XL encoder wrapper.
</p>
<p>Requires the presence of the libjxl headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libjxl</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-31" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-31">
<h4 class="subsection"><span>9.9.1 Options<a class="copiable-link" href="#Options-31"> &para;</a></span></h4>

<p>The libjxl wrapper supports the following options:
</p>
<dl class="table">
<dt><samp class="option">distance</samp></dt>
<dd><p>Set the target Butteraugli distance. This is a quality setting: lower
distance yields higher quality, with distance=1.0 roughly comparable to
libjpeg Quality 90 for photographic content. Setting distance=0.0 yields
true lossless encoding. Valid values range between 0.0 and 15.0, and sane
values rarely exceed 5.0. Setting distance=0.1 usually attains
transparency for most input. The default is 1.0.
</p>
</dd>
<dt><samp class="option">effort</samp></dt>
<dd><p>Set the encoding effort used. Higher effort values produce more consistent
quality and usually produces a better quality/bpp curve, at the cost of
more CPU time required. Valid values range from 1 to 9, and the default is 7.
</p>
</dd>
<dt><samp class="option">modular</samp></dt>
<dd><p>Force the encoder to use Modular mode instead of choosing automatically. The
default is to use VarDCT for lossy encoding and Modular for lossless. VarDCT
is generally superior to Modular for lossy encoding but does not support
lossless encoding.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libkvazaar">
<h3 class="section"><span>9.10 libkvazaar<a class="copiable-link" href="#libkvazaar"> &para;</a></span></h3>

<p>Kvazaar H.265/HEVC encoder.
</p>
<p>Requires the presence of the libkvazaar headers and library during
configuration. You need to explicitly configure the build with
<samp class="option">--enable-libkvazaar</samp>.
</p>
<ul class="mini-toc">
<li><a href="#Options-32" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-32">
<h4 class="subsection"><span>9.10.1 Options<a class="copiable-link" href="#Options-32"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set target video bitrate in bit/s and enable rate control.
</p>
</dd>
<dt><samp class="option">kvazaar-params</samp></dt>
<dd><p>Set kvazaar parameters as a list of <var class="var">name</var>=<var class="var">value</var> pairs separated
by commas (,). See kvazaar documentation for a list of options.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libopenh264">
<h3 class="section"><span>9.11 libopenh264<a class="copiable-link" href="#libopenh264"> &para;</a></span></h3>

<p>Cisco libopenh264 H.264/MPEG-4 AVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libopenh264 headers and
library during configuration. You need to explicitly configure the
build with <code class="code">--enable-libopenh264</code>. The library is detected using
<code class="command">pkg-config</code>.
</p>
<p>For more information about the library see
<a class="url" href="http://www.openh264.org">http://www.openh264.org</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-33" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-33">
<h4 class="subsection"><span>9.11.1 Options<a class="copiable-link" href="#Options-33"> &para;</a></span></h4>

<p>The following FFmpeg global options affect the configurations of the
libopenh264 encoder.
</p>
<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set the bitrate (as a number of bits per second).
</p>
</dd>
<dt><samp class="option">g</samp></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><samp class="option">maxrate</samp></dt>
<dd><p>Set the max bitrate (as a number of bits per second).
</p>
</dd>
<dt><samp class="option">flags +global_header</samp></dt>
<dd><p>Set global header in the bitstream.
</p>
</dd>
<dt><samp class="option">slices</samp></dt>
<dd><p>Set the number of slices, used in parallelized encoding. Default value
is 0. This is only used when <samp class="option">slice_mode</samp> is set to
&lsquo;<samp class="samp">fixed</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">loopfilter</samp></dt>
<dd><p>Enable loop filter, if set to 1 (automatically enabled). To disable
set a value of 0.
</p>
</dd>
<dt><samp class="option">profile</samp></dt>
<dd><p>Set profile restrictions. If set to the value of &lsquo;<samp class="samp">main</samp>&rsquo; enable
CABAC (set the <code class="code">SEncParamExt.iEntropyCodingModeFlag</code> flag to 1).
</p>
</dd>
<dt><samp class="option">max_nal_size</samp></dt>
<dd><p>Set maximum NAL size in bytes.
</p>
</dd>
<dt><samp class="option">allow_skip_frames</samp></dt>
<dd><p>Allow skipping frames to hit the target bitrate if set to 1.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libtheora">
<h3 class="section"><span>9.12 libtheora<a class="copiable-link" href="#libtheora"> &para;</a></span></h3>

<p>libtheora Theora encoder wrapper.
</p>
<p>Requires the presence of the libtheora headers and library during
configuration. You need to explicitly configure the build with
<code class="code">--enable-libtheora</code>.
</p>
<p>For more information about the libtheora project see
<a class="url" href="http://www.theora.org/">http://www.theora.org/</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-34" accesskey="1">Options</a></li>
<li><a href="#Examples-1" accesskey="2">Examples</a></li>
</ul>
<div class="subsection-level-extent" id="Options-34">
<h4 class="subsection"><span>9.12.1 Options<a class="copiable-link" href="#Options-34"> &para;</a></span></h4>

<p>The following global options are mapped to internal libtheora options
which affect the quality and the bitrate of the encoded stream.
</p>
<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Set the video bitrate in bit/s for CBR (Constant Bit Rate) mode.  In
case VBR (Variable Bit Rate) mode is enabled this option is ignored.
</p>
</dd>
<dt><samp class="option">flags</samp></dt>
<dd><p>Used to enable constant quality mode (VBR) encoding through the
<samp class="option">qscale</samp> flag, and to enable the <code class="code">pass1</code> and <code class="code">pass2</code>
modes.
</p>
</dd>
<dt><samp class="option">g</samp></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><samp class="option">global_quality</samp></dt>
<dd><p>Set the global quality as an integer in lambda units.
</p>
<p>Only relevant when VBR mode is enabled with <code class="code">flags +qscale</code>. The
value is converted to QP units by dividing it by <code class="code">FF_QP2LAMBDA</code>,
clipped in the [0 - 10] range, and then multiplied by 6.3 to get a
value in the native libtheora range [0-63]. A higher value corresponds
to a higher quality.
</p>
</dd>
<dt><samp class="option">q</samp></dt>
<dd><p>Enable VBR mode when set to a non-negative value, and set constant
quality value as a double floating point value in QP units.
</p>
<p>The value is clipped in the [0-10] range, and then multiplied by 6.3
to get a value in the native libtheora range [0-63].
</p>
<p>This option is valid only using the <code class="command">ffmpeg</code> command-line
tool. For library interface users, use <samp class="option">global_quality</samp>.
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="Examples-1">
<h4 class="subsection"><span>9.12.2 Examples<a class="copiable-link" href="#Examples-1"> &para;</a></span></h4>

<ul class="itemize mark-bullet">
<li>Set maximum constant quality (VBR) encoding with <code class="command">ffmpeg</code>:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -codec:v libtheora -q:v 10 OUTPUT.ogg
</pre></div>

</li><li>Use <code class="command">ffmpeg</code> to convert a CBR 1000 kbps Theora video stream:
<div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -codec:v libtheora -b:v 1000k OUTPUT.ogg
</pre></div>
</li></ul>

</div>
</div>
<div class="section-level-extent" id="libvpx">
<h3 class="section"><span>9.13 libvpx<a class="copiable-link" href="#libvpx"> &para;</a></span></h3>

<p>VP8/VP9 format supported through libvpx.
</p>
<p>Requires the presence of the libvpx headers and library during configuration.
You need to explicitly configure the build with <code class="code">--enable-libvpx</code>.
</p>
<ul class="mini-toc">
<li><a href="#Options-35" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-35">
<h4 class="subsection"><span>9.13.1 Options<a class="copiable-link" href="#Options-35"> &para;</a></span></h4>

<p>The following options are supported by the libvpx wrapper. The
<code class="command">vpxenc</code>-equivalent options or values are listed in parentheses
for easy migration.
</p>
<p>To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
<a class="ref" href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<p>To get more documentation of the libvpx options, invoke the command
<code class="command">ffmpeg -h encoder=libvpx</code>, <code class="command">ffmpeg -h encoder=libvpx-vp9</code> or
<code class="command">vpxenc --help</code>. Further information is available in the libvpx API
documentation.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">target-bitrate</em>)</samp></dt>
<dd><p>Set bitrate in bits/s. Note that FFmpeg&rsquo;s <samp class="option">b</samp> option is
expressed in bits/s, while <code class="command">vpxenc</code>&rsquo;s <samp class="option">target-bitrate</samp> is in
kilobits/s.
</p>
</dd>
<dt><samp class="option">g (<em class="emph">kf-max-dist</em>)</samp></dt>
<dt><samp class="option">keyint_min (<em class="emph">kf-min-dist</em>)</samp></dt>
<dt><samp class="option">qmin (<em class="emph">min-q</em>)</samp></dt>
<dd><p>Minimum (Best Quality) Quantizer.
</p>
</dd>
<dt><samp class="option">qmax (<em class="emph">max-q</em>)</samp></dt>
<dd><p>Maximum (Worst Quality) Quantizer.
Can be changed per-frame.
</p>
</dd>
<dt><samp class="option">bufsize (<em class="emph">buf-sz</em>, <em class="emph">buf-optimal-sz</em>)</samp></dt>
<dd><p>Set ratecontrol buffer size (in bits). Note <code class="command">vpxenc</code>&rsquo;s options are
specified in milliseconds, the libvpx wrapper converts this value as follows:
<code class="code">buf-sz = bufsize * 1000 / bitrate</code>,
<code class="code">buf-optimal-sz = bufsize * 1000 / bitrate * 5 / 6</code>.
</p>
</dd>
<dt><samp class="option">rc_init_occupancy (<em class="emph">buf-initial-sz</em>)</samp></dt>
<dd><p>Set number of bits which should be loaded into the rc buffer before decoding
starts. Note <code class="command">vpxenc</code>&rsquo;s option is specified in milliseconds, the libvpx
wrapper converts this value as follows:
<code class="code">rc_init_occupancy * 1000 / bitrate</code>.
</p>
</dd>
<dt><samp class="option">undershoot-pct</samp></dt>
<dd><p>Set datarate undershoot (min) percentage of the target bitrate.
</p>
</dd>
<dt><samp class="option">overshoot-pct</samp></dt>
<dd><p>Set datarate overshoot (max) percentage of the target bitrate.
</p>
</dd>
<dt><samp class="option">skip_threshold (<em class="emph">drop-frame</em>)</samp></dt>
<dt><samp class="option">qcomp (<em class="emph">bias-pct</em>)</samp></dt>
<dt><samp class="option">maxrate (<em class="emph">maxsection-pct</em>)</samp></dt>
<dd><p>Set GOP max bitrate in bits/s. Note <code class="command">vpxenc</code>&rsquo;s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: <code class="code">(maxrate * 100 / bitrate)</code>.
</p>
</dd>
<dt><samp class="option">minrate (<em class="emph">minsection-pct</em>)</samp></dt>
<dd><p>Set GOP min bitrate in bits/s. Note <code class="command">vpxenc</code>&rsquo;s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: <code class="code">(minrate * 100 / bitrate)</code>.
</p>
</dd>
<dt><samp class="option">minrate, maxrate, b <em class="emph">end-usage=cbr</em></samp></dt>
<dd><p><code class="code">(minrate == maxrate == bitrate)</code>.
</p>
</dd>
<dt><samp class="option">crf (<em class="emph">end-usage=cq</em>, <em class="emph">cq-level</em>)</samp></dt>
<dt><samp class="option">tune (<em class="emph">tune</em>)</samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">psnr (<em class="emph">psnr</em>)</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">ssim (<em class="emph">ssim</em>)</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">quality, deadline (<em class="emph">deadline</em>)</samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">best</samp>&rsquo;</dt>
<dd><p>Use best quality deadline. Poorly named and quite slow, this option should be
avoided as it may give worse quality output than good.
</p></dd>
<dt>&lsquo;<samp class="samp">good</samp>&rsquo;</dt>
<dd><p>Use good quality deadline. This is a good trade-off between speed and quality
when used with the <samp class="option">cpu-used</samp> option.
</p></dd>
<dt>&lsquo;<samp class="samp">realtime</samp>&rsquo;</dt>
<dd><p>Use realtime quality deadline.
</p></dd>
</dl>

</dd>
<dt><samp class="option">speed, cpu-used (<em class="emph">cpu-used</em>)</samp></dt>
<dd><p>Set quality/speed ratio modifier. Higher values speed up the encode at the cost
of quality.
</p>
</dd>
<dt><samp class="option">nr (<em class="emph">noise-sensitivity</em>)</samp></dt>
<dt><samp class="option">static-thresh</samp></dt>
<dd><p>Set a change threshold on blocks below which they will be skipped by the
encoder.
</p>
</dd>
<dt><samp class="option">slices (<em class="emph">token-parts</em>)</samp></dt>
<dd><p>Note that FFmpeg&rsquo;s <samp class="option">slices</samp> option gives the total number of partitions,
while <code class="command">vpxenc</code>&rsquo;s <samp class="option">token-parts</samp> is given as
<code class="code">log2(partitions)</code>.
</p>
</dd>
<dt><samp class="option">max-intra-rate</samp></dt>
<dd><p>Set maximum I-frame bitrate as a percentage of the target bitrate. A value of 0
means unlimited.
</p>
</dd>
<dt><samp class="option">force_key_frames</samp></dt>
<dd><p><code class="code">VPX_EFLAG_FORCE_KF</code>
</p>
</dd>
<dt><samp class="option">Alternate reference frame related</samp></dt>
<dd><dl class="table">
<dt><samp class="option">auto-alt-ref</samp></dt>
<dd><p>Enable use of alternate reference frames (2-pass only).
Values greater than 1 enable multi-layer alternate reference frames (VP9 only).
</p></dd>
<dt><samp class="option">arnr-maxframes</samp></dt>
<dd><p>Set altref noise reduction max frame count.
</p></dd>
<dt><samp class="option">arnr-type</samp></dt>
<dd><p>Set altref noise reduction filter type: backward, forward, centered.
</p></dd>
<dt><samp class="option">arnr-strength</samp></dt>
<dd><p>Set altref noise reduction filter strength.
</p></dd>
<dt><samp class="option">rc-lookahead, lag-in-frames (<em class="emph">lag-in-frames</em>)</samp></dt>
<dd><p>Set number of frames to look ahead for frametype and ratecontrol.
</p></dd>
<dt><samp class="option">min-gf-interval</samp></dt>
<dd><p>Set minimum golden/alternate reference frame interval (VP9 only).
</p></dd>
</dl>

</dd>
<dt><samp class="option">error-resilient</samp></dt>
<dd><p>Enable error resiliency features.
</p>
</dd>
<dt><samp class="option">sharpness <var class="var">integer</var></samp></dt>
<dd><p>Increase sharpness at the expense of lower PSNR.
The valid range is [0, 7].
</p>
</dd>
<dt><samp class="option">ts-parameters</samp></dt>
<dd><p>Sets the temporal scalability configuration using a :-separated list of
key=value pairs. For example, to specify temporal scalability parameters
with <code class="code">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -c:v libvpx -ts-parameters ts_number_layers=3:\
ts_target_bitrate=250,500,1000:ts_rate_decimator=4,2,1:\
ts_periodicity=4:ts_layer_id=0,2,1,2:ts_layering_mode=3 OUTPUT
</pre></div>
<p>Below is a brief explanation of each of the parameters, please
refer to <code class="code">struct vpx_codec_enc_cfg</code> in <code class="code">vpx/vpx_encoder.h</code> for more
details.
</p><dl class="table">
<dt><samp class="option">ts_number_layers</samp></dt>
<dd><p>Number of temporal coding layers.
</p></dd>
<dt><samp class="option">ts_target_bitrate</samp></dt>
<dd><p>Target bitrate for each temporal layer (in kbps).
(bitrate should be inclusive of the lower temporal layer).
</p></dd>
<dt><samp class="option">ts_rate_decimator</samp></dt>
<dd><p>Frame rate decimation factor for each temporal layer.
</p></dd>
<dt><samp class="option">ts_periodicity</samp></dt>
<dd><p>Length of the sequence defining frame temporal layer membership.
</p></dd>
<dt><samp class="option">ts_layer_id</samp></dt>
<dd><p>Template defining the membership of frames to temporal layers.
</p></dd>
<dt><samp class="option">ts_layering_mode</samp></dt>
<dd><p>(optional) Selecting the temporal structure from a set of pre-defined temporal layering modes.
Currently supports the following options.
</p><dl class="table">
<dt><samp class="option">0</samp></dt>
<dd><p>No temporal layering flags are provided internally,
relies on flags being passed in using <code class="code">metadata</code> field in <code class="code">AVFrame</code>
with following keys.
</p><dl class="table">
<dt><samp class="option">vp8-flags</samp></dt>
<dd><p>Sets the flags passed into the encoder to indicate the referencing scheme for
the current frame.
Refer to function <code class="code">vpx_codec_encode</code> in <code class="code">vpx/vpx_encoder.h</code> for more
details.
</p></dd>
<dt><samp class="option">temporal_id</samp></dt>
<dd><p>Explicitly sets the temporal id of the current frame to encode.
</p></dd>
</dl>
</dd>
<dt><samp class="option">2</samp></dt>
<dd><p>Two temporal layers. 0-1...
</p></dd>
<dt><samp class="option">3</samp></dt>
<dd><p>Three temporal layers. 0-2-1-2...; with single reference frame.
</p></dd>
<dt><samp class="option">4</samp></dt>
<dd><p>Same as option &quot;3&quot;, except there is a dependency between
the two temporal layer 2 frames within the temporal period.
</p></dd>
</dl>
</dd>
</dl>

</dd>
<dt><samp class="option">VP8-specific options</samp></dt>
<dd><dl class="table">
<dt><samp class="option">screen-content-mode</samp></dt>
<dd><p>Screen content mode, one of: 0 (off), 1 (screen), 2 (screen with more aggressive rate control).
</p></dd>
</dl>

</dd>
<dt><samp class="option">VP9-specific options</samp></dt>
<dd><dl class="table">
<dt><samp class="option">lossless</samp></dt>
<dd><p>Enable lossless mode.
</p></dd>
<dt><samp class="option">tile-columns</samp></dt>
<dd><p>Set number of tile columns to use. Note this is given as
<code class="code">log2(tile_columns)</code>. For example, 8 tile columns would be requested by
setting the <samp class="option">tile-columns</samp> option to 3.
</p></dd>
<dt><samp class="option">tile-rows</samp></dt>
<dd><p>Set number of tile rows to use. Note this is given as <code class="code">log2(tile_rows)</code>.
For example, 4 tile rows would be requested by setting the <samp class="option">tile-rows</samp>
option to 2.
</p></dd>
<dt><samp class="option">frame-parallel</samp></dt>
<dd><p>Enable frame parallel decodability features.
</p></dd>
<dt><samp class="option">aq-mode</samp></dt>
<dd><p>Set adaptive quantization mode (0: off (default), 1: variance 2: complexity, 3:
cyclic refresh, 4: equator360).
</p></dd>
<dt><samp class="option">colorspace <em class="emph">color-space</em></samp></dt>
<dd><p>Set input color space. The VP9 bitstream supports signaling the following
colorspaces:
</p><dl class="table">
<dt><samp class="option">&lsquo;<samp class="samp">rgb</samp>&rsquo; <em class="emph">sRGB</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">bt709</samp>&rsquo; <em class="emph">bt709</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">unspecified</samp>&rsquo; <em class="emph">unknown</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">bt470bg</samp>&rsquo; <em class="emph">bt601</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">smpte170m</samp>&rsquo; <em class="emph">smpte170</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">smpte240m</samp>&rsquo; <em class="emph">smpte240</em></samp></dt>
<dt><samp class="option">&lsquo;<samp class="samp">bt2020_ncl</samp>&rsquo; <em class="emph">bt2020</em></samp></dt>
</dl>
</dd>
<dt><samp class="option">row-mt <var class="var">boolean</var></samp></dt>
<dd><p>Enable row based multi-threading.
</p></dd>
<dt><samp class="option">tune-content</samp></dt>
<dd><p>Set content type: default (0), screen (1), film (2).
</p></dd>
<dt><samp class="option">corpus-complexity</samp></dt>
<dd><p>Corpus VBR mode is a variant of standard VBR where the complexity distribution
midpoint is passed in rather than calculated for a specific clip or chunk.
</p>
<p>The valid range is [0, 10000]. 0 (default) uses standard VBR.
</p></dd>
<dt><samp class="option">enable-tpl <var class="var">boolean</var></samp></dt>
<dd><p>Enable temporal dependency model.
</p></dd>
<dt><samp class="option">ref-frame-config</samp></dt>
<dd><p>Using per-frame metadata, set members of the structure <code class="code">vpx_svc_ref_frame_config_t</code> in <code class="code">vpx/vp8cx.h</code> to fine-control referencing schemes and frame buffer management.
<br>Use a :-separated list of key=value pairs.
For example,
</p><div class="example">
<pre class="example-preformatted">av_dict_set(&amp;av_frame-&gt;metadata, &quot;ref-frame-config&quot;, \
&quot;rfc_update_buffer_slot=7:rfc_lst_fb_idx=0:rfc_gld_fb_idx=1:rfc_alt_fb_idx=2:rfc_reference_last=0:rfc_reference_golden=0:rfc_reference_alt_ref=0&quot;);
</pre></div>
<dl class="table">
<dt><samp class="option">rfc_update_buffer_slot</samp></dt>
<dd><p>Indicates the buffer slot number to update
</p></dd>
<dt><samp class="option">rfc_update_last</samp></dt>
<dd><p>Indicates whether to update the LAST frame
</p></dd>
<dt><samp class="option">rfc_update_golden</samp></dt>
<dd><p>Indicates whether to update GOLDEN frame
</p></dd>
<dt><samp class="option">rfc_update_alt_ref</samp></dt>
<dd><p>Indicates whether to update ALT_REF frame
</p></dd>
<dt><samp class="option">rfc_lst_fb_idx</samp></dt>
<dd><p>LAST frame buffer index
</p></dd>
<dt><samp class="option">rfc_gld_fb_idx</samp></dt>
<dd><p>GOLDEN frame buffer index
</p></dd>
<dt><samp class="option">rfc_alt_fb_idx</samp></dt>
<dd><p>ALT_REF frame buffer index
</p></dd>
<dt><samp class="option">rfc_reference_last</samp></dt>
<dd><p>Indicates whether to reference LAST frame
</p></dd>
<dt><samp class="option">rfc_reference_golden</samp></dt>
<dd><p>Indicates whether to reference GOLDEN frame
</p></dd>
<dt><samp class="option">rfc_reference_alt_ref</samp></dt>
<dd><p>Indicates whether to reference ALT_REF frame
</p></dd>
<dt><samp class="option">rfc_reference_duration</samp></dt>
<dd><p>Indicates frame duration
</p></dd>
</dl>
</dd>
</dl>

</dd>
</dl>

<p>For more information about libvpx see:
<a class="url" href="http://www.webmproject.org/">http://www.webmproject.org/</a>
</p>
</div>
</div>
<div class="section-level-extent" id="libvvenc">
<h3 class="section"><span>9.14 libvvenc<a class="copiable-link" href="#libvvenc"> &para;</a></span></h3>

<p>VVenC H.266/VVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libvvenc headers and library
during configuration. You need to explicitly configure the build with
<samp class="option">--enable-libvvenc</samp>.
</p>
<p>The VVenC project website is at
<a class="url" href="https://github.com/fraunhoferhhi/vvenc">https://github.com/fraunhoferhhi/vvenc</a>.
</p>
<ul class="mini-toc">
<li><a href="#Supported-Pixel-Formats" accesskey="1">Supported Pixel Formats</a></li>
<li><a href="#Options-36" accesskey="2">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Supported-Pixel-Formats">
<h4 class="subsection"><span>9.14.1 Supported Pixel Formats<a class="copiable-link" href="#Supported-Pixel-Formats"> &para;</a></span></h4>

<p>VVenC supports only 10-bit color spaces as input. But the internal (encoded)
bit depth can be set to 8-bit or 10-bit at runtime.
</p>
</div>
<div class="subsection-level-extent" id="Options-36">
<h4 class="subsection"><span>9.14.2 Options<a class="copiable-link" href="#Options-36"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Sets target video bitrate.
</p>
</dd>
<dt><samp class="option">g</samp></dt>
<dd><p>Set the GOP size. Currently support for g=1 (Intra only) or default.
</p>
</dd>
<dt><samp class="option">preset</samp></dt>
<dd><p>Set the VVenC preset.
</p>
</dd>
<dt><samp class="option">levelidc</samp></dt>
<dd><p>Set level idc.
</p>
</dd>
<dt><samp class="option">tier</samp></dt>
<dd><p>Set vvc tier.
</p>
</dd>
<dt><samp class="option">qp</samp></dt>
<dd><p>Set constant quantization parameter.
</p>
</dd>
<dt><samp class="option">subopt <var class="var">boolean</var></samp></dt>
<dd><p>Set subjective (perceptually motivated) optimization. Default is 1 (on).
</p>
</dd>
<dt><samp class="option">bitdepth8 <var class="var">boolean</var></samp></dt>
<dd><p>Set 8bit coding mode instead of using 10bit. Default is 0 (off).
</p>
</dd>
<dt><samp class="option">period</samp></dt>
<dd><p>set (intra) refresh period in seconds.
</p>
</dd>
<dt><samp class="option">vvenc-params</samp></dt>
<dd><p>Set vvenc options using a list of <var class="var">key</var>=<var class="var">value</var> couples separated
by &quot;:&quot;. See <code class="command">vvencapp --fullhelp</code> or <code class="command">vvencFFapp --fullhelp</code> for a list of options.
</p>
<p>For example, the options might be provided as:
</p>
<div class="example">
<pre class="example-preformatted">intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8
</pre></div>

<p>For example the encoding options might be provided with <samp class="option">-vvenc-params</samp>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input -c:v libvvenc -b 1M -vvenc-params intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8 output.mp4
</pre></div>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libwebp">
<h3 class="section"><span>9.15 libwebp<a class="copiable-link" href="#libwebp"> &para;</a></span></h3>

<p>libwebp WebP Image encoder wrapper
</p>
<p>libwebp is Google&rsquo;s official encoder for WebP images. It can encode in either
lossy or lossless mode. Lossy images are essentially a wrapper around a VP8
frame. Lossless images are a separate codec developed by Google.
</p>
<ul class="mini-toc">
<li><a href="#Pixel-Format" accesskey="1">Pixel Format</a></li>
<li><a href="#Options-37" accesskey="2">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Pixel-Format">
<h4 class="subsection"><span>9.15.1 Pixel Format<a class="copiable-link" href="#Pixel-Format"> &para;</a></span></h4>

<p>Currently, libwebp only supports YUV420 for lossy and RGB for lossless due
to limitations of the format and libwebp. Alpha is supported for either mode.
Because of API limitations, if RGB is passed in when encoding lossy or YUV is
passed in for encoding lossless, the pixel format will automatically be
converted using functions from libwebp. This is not ideal and is done only for
convenience.
</p>
</div>
<div class="subsection-level-extent" id="Options-37">
<h4 class="subsection"><span>9.15.2 Options<a class="copiable-link" href="#Options-37"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">-lossless <var class="var">boolean</var></samp></dt>
<dd><p>Enables/Disables use of lossless mode. Default is 0.
</p>
</dd>
<dt><samp class="option">-compression_level <var class="var">integer</var></samp></dt>
<dd><p>For lossy, this is a quality/speed tradeoff. Higher values give better quality
for a given size at the cost of increased encoding time. For lossless, this is
a size/speed tradeoff. Higher values give smaller size at the cost of increased
encoding time. More specifically, it controls the number of extra algorithms
and compression tools used, and varies the combination of these tools. This
maps to the <var class="var">method</var> option in libwebp. The valid range is 0 to 6.
Default is 4.
</p>
</dd>
<dt><samp class="option">-quality <var class="var">float</var></samp></dt>
<dd><p>For lossy encoding, this controls image quality. For lossless encoding, this
controls the effort and time spent in compression.
Range is 0 to 100. Default is 75.
</p>
</dd>
<dt><samp class="option">-preset <var class="var">type</var></samp></dt>
<dd><p>Configuration preset. This does some automatic settings based on the general
type of the image.
</p><dl class="table">
<dt><samp class="option">none</samp></dt>
<dd><p>Do not use a preset.
</p></dd>
<dt><samp class="option">default</samp></dt>
<dd><p>Use the encoder default.
</p></dd>
<dt><samp class="option">picture</samp></dt>
<dd><p>Digital picture, like portrait, inner shot
</p></dd>
<dt><samp class="option">photo</samp></dt>
<dd><p>Outdoor photograph, with natural lighting
</p></dd>
<dt><samp class="option">drawing</samp></dt>
<dd><p>Hand or line drawing, with high-contrast details
</p></dd>
<dt><samp class="option">icon</samp></dt>
<dd><p>Small-sized colorful images
</p></dd>
<dt><samp class="option">text</samp></dt>
<dd><p>Text-like
</p></dd>
</dl>

</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libx264_002c-libx264rgb">
<h3 class="section"><span>9.16 libx264, libx264rgb<a class="copiable-link" href="#libx264_002c-libx264rgb"> &para;</a></span></h3>

<p>x264 H.264/MPEG-4 AVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libx264 headers and library
during configuration. You need to explicitly configure the build with
<code class="code">--enable-libx264</code>.
</p>
<p>libx264 supports an impressive number of features, including 8x8 and
4x4 adaptive spatial transform, adaptive B-frame placement, CAVLC/CABAC
entropy coding, interlacing (MBAFF), lossless mode, psy optimizations
for detail retention (adaptive quantization, psy-RD, psy-trellis).
</p>
<p>Many libx264 encoder options are mapped to FFmpeg global codec
options, while unique encoder options are provided through private
options. Additionally the <samp class="option">x264opts</samp> and <samp class="option">x264-params</samp>
private options allows one to pass a list of key=value tuples as accepted
by the libx264 <code class="code">x264_param_parse</code> function.
</p>
<p>The x264 project website is at
<a class="url" href="http://www.videolan.org/developers/x264.html">http://www.videolan.org/developers/x264.html</a>.
</p>
<p>The libx264rgb encoder is the same as libx264, except it accepts packed RGB
pixel formats as input instead of YUV.
</p>
<ul class="mini-toc">
<li><a href="#Supported-Pixel-Formats-1" accesskey="1">Supported Pixel Formats</a></li>
<li><a href="#Options-38" accesskey="2">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Supported-Pixel-Formats-1">
<h4 class="subsection"><span>9.16.1 Supported Pixel Formats<a class="copiable-link" href="#Supported-Pixel-Formats-1"> &para;</a></span></h4>

<p>x264 supports 8- to 10-bit color spaces. The exact bit depth is controlled at
x264&rsquo;s configure time.
</p>
</div>
<div class="subsection-level-extent" id="Options-38">
<h4 class="subsection"><span>9.16.2 Options<a class="copiable-link" href="#Options-38"> &para;</a></span></h4>

<p>The following options are supported by the libx264 wrapper. The
<code class="command">x264</code>-equivalent options or values are listed in parentheses
for easy migration.
</p>
<p>To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
<a class="ref" href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<p>To get a more accurate and extensive documentation of the libx264
options, invoke the command <code class="command">x264 --fullhelp</code> or consult
the libx264 documentation.
</p>
<p>In the list below, note that the <code class="command">x264</code> option name is shown
in parentheses after the libavcodec corresponding name, in case there
is a direct mapping.
</p>
<dl class="table">
<dt><samp class="option">b (<em class="emph">bitrate</em>)</samp></dt>
<dd><p>Set bitrate in bits/s. Note that FFmpeg&rsquo;s <samp class="option">b</samp> option is
expressed in bits/s, while <code class="command">x264</code>&rsquo;s <samp class="option">bitrate</samp> is in
kilobits/s.
</p>
</dd>
<dt><samp class="option">bf (<em class="emph">bframes</em>)</samp></dt>
<dd><p>Number of B-frames between I and P-frames
</p>
</dd>
<dt><samp class="option">g (<em class="emph">keyint</em>)</samp></dt>
<dd><p>Maximum GOP size
</p>
</dd>
<dt><samp class="option">qmin (<em class="emph">qpmin</em>)</samp></dt>
<dd><p>Minimum quantizer scale
</p>
</dd>
<dt><samp class="option">qmax (<em class="emph">qpmax</em>)</samp></dt>
<dd><p>Maximum quantizer scale
</p>
</dd>
<dt><samp class="option">qdiff (<em class="emph">qpstep</em>)</samp></dt>
<dd><p>Maximum difference between quantizer scales
</p>
</dd>
<dt><samp class="option">qblur (<em class="emph">qblur</em>)</samp></dt>
<dd><p>Quantizer curve blur
</p>
</dd>
<dt><samp class="option">qcomp (<em class="emph">qcomp</em>)</samp></dt>
<dd><p>Quantizer curve compression factor
</p>
</dd>
<dt><samp class="option">refs (<em class="emph">ref</em>)</samp></dt>
<dd><p>Number of reference frames each P-frame can use. The range is <var class="var">0-16</var>.
</p>
</dd>
<dt><samp class="option">level (<em class="emph">level</em>)</samp></dt>
<dd><p>Set the <code class="code">x264_param_t.i_level_idc</code> value in case the value is
positive, it is ignored otherwise.
</p>
<p>This value can be set using the <code class="code">AVCodecContext</code> API (e.g. by
setting the <code class="code">AVCodecContext</code> value directly), and is specified as
an integer mapped on a corresponding level (e.g. the value 31 maps
to H.264 level IDC &quot;3.1&quot;, as defined in the <code class="code">x264_levels</code>
table). It is ignored when set to a non positive value.
</p>
<p>Alternatively it can be set as a private option, overriding the value
set in <code class="code">AVCodecContext</code>, and in this case must be specified as
the level IDC identifier (e.g. &quot;3.1&quot;), as defined by H.264 Annex A.
</p>
</dd>
<dt><samp class="option">sc_threshold (<em class="emph">scenecut</em>)</samp></dt>
<dd><p>Sets the threshold for the scene change detection.
</p>
</dd>
<dt><samp class="option">trellis (<em class="emph">trellis</em>)</samp></dt>
<dd><p>Performs Trellis quantization to increase efficiency. Enabled by default.
</p>
</dd>
<dt><samp class="option">nr (<em class="emph">nr</em>)</samp></dt>
<dd><p>Noise reduction
</p>
</dd>
<dt><samp class="option">me_range (<em class="emph">merange</em>)</samp></dt>
<dd><p>Maximum range of the motion search in pixels.
</p>
</dd>
<dt><samp class="option">me_method (<em class="emph">me</em>)</samp></dt>
<dd><p>Set motion estimation method. Possible values in the decreasing order
of speed:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">dia (<em class="emph">dia</em>)</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">epzs (<em class="emph">dia</em>)</samp>&rsquo;</dt>
<dd><p>Diamond search with radius 1 (fastest). &lsquo;<samp class="samp">epzs</samp>&rsquo; is an alias for
&lsquo;<samp class="samp">dia</samp>&rsquo;.
</p></dd>
<dt>&lsquo;<samp class="samp">hex (<em class="emph">hex</em>)</samp>&rsquo;</dt>
<dd><p>Hexagonal search with radius 2.
</p></dd>
<dt>&lsquo;<samp class="samp">umh (<em class="emph">umh</em>)</samp>&rsquo;</dt>
<dd><p>Uneven multi-hexagon search.
</p></dd>
<dt>&lsquo;<samp class="samp">esa (<em class="emph">esa</em>)</samp>&rsquo;</dt>
<dd><p>Exhaustive search.
</p></dd>
<dt>&lsquo;<samp class="samp">tesa (<em class="emph">tesa</em>)</samp>&rsquo;</dt>
<dd><p>Hadamard exhaustive search (slowest).
</p></dd>
</dl>

</dd>
<dt><samp class="option">forced-idr</samp></dt>
<dd><p>Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.
</p>
</dd>
<dt><samp class="option">subq (<em class="emph">subme</em>)</samp></dt>
<dd><p>Sub-pixel motion estimation method.
</p>
</dd>
<dt><samp class="option">b_strategy (<em class="emph">b-adapt</em>)</samp></dt>
<dd><p>Adaptive B-frame placement decision algorithm. Use only on first-pass.
</p>
</dd>
<dt><samp class="option">keyint_min (<em class="emph">min-keyint</em>)</samp></dt>
<dd><p>Minimum GOP size.
</p>
</dd>
<dt><samp class="option">coder</samp></dt>
<dd><p>Set entropy encoder. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">ac</samp>&rsquo;</dt>
<dd><p>Enable CABAC.
</p>
</dd>
<dt>&lsquo;<samp class="samp">vlc</samp>&rsquo;</dt>
<dd><p>Enable CAVLC and disable CABAC. It generates the same effect as
<code class="command">x264</code>&rsquo;s <samp class="option">--no-cabac</samp> option.
</p></dd>
</dl>

</dd>
<dt><samp class="option">cmp</samp></dt>
<dd><p>Set full pixel motion estimation comparison algorithm. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">chroma</samp>&rsquo;</dt>
<dd><p>Enable chroma in motion estimation.
</p>
</dd>
<dt>&lsquo;<samp class="samp">sad</samp>&rsquo;</dt>
<dd><p>Ignore chroma in motion estimation. It generates the same effect as
<code class="command">x264</code>&rsquo;s <samp class="option">--no-chroma-me</samp> option.
</p></dd>
</dl>

</dd>
<dt><samp class="option">threads (<em class="emph">threads</em>)</samp></dt>
<dd><p>Number of encoding threads.
</p>
</dd>
<dt><samp class="option">thread_type</samp></dt>
<dd><p>Set multithreading technique. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">slice</samp>&rsquo;</dt>
<dd><p>Slice-based multithreading. It generates the same effect as
<code class="command">x264</code>&rsquo;s <samp class="option">--sliced-threads</samp> option.
</p></dd>
<dt>&lsquo;<samp class="samp">frame</samp>&rsquo;</dt>
<dd><p>Frame-based multithreading.
</p></dd>
</dl>

</dd>
<dt><samp class="option">flags</samp></dt>
<dd><p>Set encoding flags. It can be used to disable closed GOP and enable
open GOP by setting it to <code class="code">-cgop</code>. The result is similar to
the behavior of <code class="command">x264</code>&rsquo;s <samp class="option">--open-gop</samp> option.
</p>
</dd>
<dt><samp class="option">rc_init_occupancy (<em class="emph">vbv-init</em>)</samp></dt>
<dd><p>Initial VBV buffer occupancy
</p>
</dd>
<dt><samp class="option">preset (<em class="emph">preset</em>)</samp></dt>
<dd><p>Set the encoding preset.
</p>
</dd>
<dt><samp class="option">tune (<em class="emph">tune</em>)</samp></dt>
<dd><p>Set tuning of the encoding params.
</p>
</dd>
<dt><samp class="option">profile (<em class="emph">profile</em>)</samp></dt>
<dd><p>Set profile restrictions.
</p>
</dd>
<dt><samp class="option">fastfirstpass</samp></dt>
<dd><p>Enable fast settings when encoding first pass, when set to 1. When set
to 0, it has the same effect of <code class="command">x264</code>&rsquo;s
<samp class="option">--slow-firstpass</samp> option.
</p>
</dd>
<dt><samp class="option">crf (<em class="emph">crf</em>)</samp></dt>
<dd><p>Set the quality for constant quality mode.
</p>
</dd>
<dt><samp class="option">crf_max (<em class="emph">crf-max</em>)</samp></dt>
<dd><p>In CRF mode, prevents VBV from lowering quality beyond this point.
</p>
</dd>
<dt><samp class="option">qp (<em class="emph">qp</em>)</samp></dt>
<dd><p>Set constant quantization rate control method parameter.
</p>
</dd>
<dt><samp class="option">aq-mode (<em class="emph">aq-mode</em>)</samp></dt>
<dd><p>Set AQ method. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">0</em>)</samp>&rsquo;</dt>
<dd><p>Disabled.
</p>
</dd>
<dt>&lsquo;<samp class="samp">variance (<em class="emph">1</em>)</samp>&rsquo;</dt>
<dd><p>Variance AQ (complexity mask).
</p>
</dd>
<dt>&lsquo;<samp class="samp">autovariance (<em class="emph">2</em>)</samp>&rsquo;</dt>
<dd><p>Auto-variance AQ (experimental).
</p></dd>
</dl>

</dd>
<dt><samp class="option">aq-strength (<em class="emph">aq-strength</em>)</samp></dt>
<dd><p>Set AQ strength, reduce blocking and blurring in flat and textured areas.
</p>
</dd>
<dt><samp class="option">psy</samp></dt>
<dd><p>Use psychovisual optimizations when set to 1. When set to 0, it has the
same effect as <code class="command">x264</code>&rsquo;s <samp class="option">--no-psy</samp> option.
</p>
</dd>
<dt><samp class="option">psy-rd (<em class="emph">psy-rd</em>)</samp></dt>
<dd><p>Set strength of psychovisual optimization, in
<var class="var">psy-rd</var>:<var class="var">psy-trellis</var> format.
</p>
</dd>
<dt><samp class="option">rc-lookahead (<em class="emph">rc-lookahead</em>)</samp></dt>
<dd><p>Set number of frames to look ahead for frametype and ratecontrol.
</p>
</dd>
<dt><samp class="option">weightb</samp></dt>
<dd><p>Enable weighted prediction for B-frames when set to 1. When set to 0,
it has the same effect as <code class="command">x264</code>&rsquo;s <samp class="option">--no-weightb</samp> option.
</p>
</dd>
<dt><samp class="option">weightp (<em class="emph">weightp</em>)</samp></dt>
<dd><p>Set weighted prediction method for P-frames. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">0</em>)</samp>&rsquo;</dt>
<dd><p>Disabled
</p></dd>
<dt>&lsquo;<samp class="samp">simple (<em class="emph">1</em>)</samp>&rsquo;</dt>
<dd><p>Enable only weighted refs
</p></dd>
<dt>&lsquo;<samp class="samp">smart (<em class="emph">2</em>)</samp>&rsquo;</dt>
<dd><p>Enable both weighted refs and duplicates
</p></dd>
</dl>

</dd>
<dt><samp class="option">ssim (<em class="emph">ssim</em>)</samp></dt>
<dd><p>Enable calculation and printing SSIM stats after the encoding.
</p>
</dd>
<dt><samp class="option">intra-refresh (<em class="emph">intra-refresh</em>)</samp></dt>
<dd><p>Enable the use of Periodic Intra Refresh instead of IDR frames when set
to 1.
</p>
</dd>
<dt><samp class="option">avcintra-class (<em class="emph">class</em>)</samp></dt>
<dd><p>Configure the encoder to generate AVC-Intra.
Valid values are 50, 100 and 200
</p>
</dd>
<dt><samp class="option">bluray-compat (<em class="emph">bluray-compat</em>)</samp></dt>
<dd><p>Configure the encoder to be compatible with the bluray standard.
It is a shorthand for setting &quot;bluray-compat=1 force-cfr=1&quot;.
</p>
</dd>
<dt><samp class="option">b-bias (<em class="emph">b-bias</em>)</samp></dt>
<dd><p>Set the influence on how often B-frames are used.
</p>
</dd>
<dt><samp class="option">b-pyramid (<em class="emph">b-pyramid</em>)</samp></dt>
<dd><p>Set method for keeping of some B-frames as references. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">none</em>)</samp>&rsquo;</dt>
<dd><p>Disabled.
</p></dd>
<dt>&lsquo;<samp class="samp">strict (<em class="emph">strict</em>)</samp>&rsquo;</dt>
<dd><p>Strictly hierarchical pyramid.
</p></dd>
<dt>&lsquo;<samp class="samp">normal (<em class="emph">normal</em>)</samp>&rsquo;</dt>
<dd><p>Non-strict (not Blu-ray compatible).
</p></dd>
</dl>

</dd>
<dt><samp class="option">mixed-refs</samp></dt>
<dd><p>Enable the use of one reference per partition, as opposed to one
reference per macroblock when set to 1. When set to 0, it has the
same effect as <code class="command">x264</code>&rsquo;s <samp class="option">--no-mixed-refs</samp> option.
</p>
</dd>
<dt><samp class="option">8x8dct</samp></dt>
<dd><p>Enable adaptive spatial transform (high profile 8x8 transform)
when set to 1. When set to 0, it has the same effect as
<code class="command">x264</code>&rsquo;s <samp class="option">--no-8x8dct</samp> option.
</p>
</dd>
<dt><samp class="option">fast-pskip</samp></dt>
<dd><p>Enable early SKIP detection on P-frames when set to 1. When set
to 0, it has the same effect as <code class="command">x264</code>&rsquo;s
<samp class="option">--no-fast-pskip</samp> option.
</p>
</dd>
<dt><samp class="option">aud (<em class="emph">aud</em>)</samp></dt>
<dd><p>Enable use of access unit delimiters when set to 1.
</p>
</dd>
<dt><samp class="option">mbtree</samp></dt>
<dd><p>Enable use macroblock tree ratecontrol when set to 1. When set
to 0, it has the same effect as <code class="command">x264</code>&rsquo;s
<samp class="option">--no-mbtree</samp> option.
</p>
</dd>
<dt><samp class="option">deblock (<em class="emph">deblock</em>)</samp></dt>
<dd><p>Set loop filter parameters, in <var class="var">alpha</var>:<var class="var">beta</var> form.
</p>
</dd>
<dt><samp class="option">cplxblur (<em class="emph">cplxblur</em>)</samp></dt>
<dd><p>Set fluctuations reduction in QP (before curve compression).
</p>
</dd>
<dt><samp class="option">partitions (<em class="emph">partitions</em>)</samp></dt>
<dd><p>Set partitions to consider as a comma-separated list of values.
Possible values in the list:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">p8x8</samp>&rsquo;</dt>
<dd><p>8x8 P-frame partition.
</p></dd>
<dt>&lsquo;<samp class="samp">p4x4</samp>&rsquo;</dt>
<dd><p>4x4 P-frame partition.
</p></dd>
<dt>&lsquo;<samp class="samp">b8x8</samp>&rsquo;</dt>
<dd><p>4x4 B-frame partition.
</p></dd>
<dt>&lsquo;<samp class="samp">i8x8</samp>&rsquo;</dt>
<dd><p>8x8 I-frame partition.
</p></dd>
<dt>&lsquo;<samp class="samp">i4x4</samp>&rsquo;</dt>
<dd><p>4x4 I-frame partition.
(Enabling &lsquo;<samp class="samp">p4x4</samp>&rsquo; requires &lsquo;<samp class="samp">p8x8</samp>&rsquo; to be enabled. Enabling
&lsquo;<samp class="samp">i8x8</samp>&rsquo; requires adaptive spatial transform (<samp class="option">8x8dct</samp>
option) to be enabled.)
</p></dd>
<dt>&lsquo;<samp class="samp">none (<em class="emph">none</em>)</samp>&rsquo;</dt>
<dd><p>Do not consider any partitions.
</p></dd>
<dt>&lsquo;<samp class="samp">all (<em class="emph">all</em>)</samp>&rsquo;</dt>
<dd><p>Consider every partition.
</p></dd>
</dl>

</dd>
<dt><samp class="option">direct-pred (<em class="emph">direct</em>)</samp></dt>
<dd><p>Set direct MV prediction mode. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">none</em>)</samp>&rsquo;</dt>
<dd><p>Disable MV prediction.
</p></dd>
<dt>&lsquo;<samp class="samp">spatial (<em class="emph">spatial</em>)</samp>&rsquo;</dt>
<dd><p>Enable spatial predicting.
</p></dd>
<dt>&lsquo;<samp class="samp">temporal (<em class="emph">temporal</em>)</samp>&rsquo;</dt>
<dd><p>Enable temporal predicting.
</p></dd>
<dt>&lsquo;<samp class="samp">auto (<em class="emph">auto</em>)</samp>&rsquo;</dt>
<dd><p>Automatically decided.
</p></dd>
</dl>

</dd>
<dt><samp class="option">slice-max-size (<em class="emph">slice-max-size</em>)</samp></dt>
<dd><p>Set the limit of the size of each slice in bytes. If not specified
but RTP payload size (<samp class="option">ps</samp>) is specified, that is used.
</p>
</dd>
<dt><samp class="option">stats (<em class="emph">stats</em>)</samp></dt>
<dd><p>Set the file name for multi-pass stats.
</p>
</dd>
<dt><samp class="option">nal-hrd (<em class="emph">nal-hrd</em>)</samp></dt>
<dd><p>Set signal HRD information (requires <samp class="option">vbv-bufsize</samp> to be set).
Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">none (<em class="emph">none</em>)</samp>&rsquo;</dt>
<dd><p>Disable HRD information signaling.
</p></dd>
<dt>&lsquo;<samp class="samp">vbr (<em class="emph">vbr</em>)</samp>&rsquo;</dt>
<dd><p>Variable bit rate.
</p></dd>
<dt>&lsquo;<samp class="samp">cbr (<em class="emph">cbr</em>)</samp>&rsquo;</dt>
<dd><p>Constant bit rate (not allowed in MP4 container).
</p></dd>
</dl>

</dd>
<dt><samp class="option">x264opts <var class="var">opts</var></samp></dt>
<dt><samp class="option">x264-params <var class="var">opts</var></samp></dt>
<dd><p>Override the x264 configuration using a :-separated list of key=value
options.
</p>
<p>The argument for both options is a list of <var class="var">key</var>=<var class="var">value</var>
couples separated by &quot;:&quot;. With <samp class="option">x264opts</samp> the value can be
omitted, and the value <code class="code">1</code> is assumed in that case.
</p>
<p>For <var class="var">filter</var> and <var class="var">psy-rd</var> options values that use &quot;:&quot; as a
separator themselves, use &quot;,&quot; instead. They accept it as well since
long ago but this is kept undocumented for some reason.
</p>
<p>For example, the options might be provided as:
</p><div class="example">
<pre class="example-preformatted">level=30:bframes=0:weightp=0:cabac=0:ref=1:vbv-maxrate=768:vbv-bufsize=2000:analyse=all:me=umh:no-fast-pskip=1:subq=6:8x8dct=0:trellis=0
</pre></div>

<p>For example to specify libx264 encoding options with <code class="command">ffmpeg</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i foo.mpg -c:v libx264 -x264opts keyint=123:min-keyint=20 -an out.mkv
</pre></div>

<p>To get the complete list of the libx264 options, invoke the command
<code class="command">x264 --fullhelp</code> or consult the libx264 documentation.
</p>
</dd>
<dt><samp class="option">a53cc <var class="var">boolean</var></samp></dt>
<dd><p>Import closed captions (which must be ATSC compatible format) into output.
Only the mpeg2 and h264 decoders provide these. Default is 1 (on).
</p>
</dd>
<dt><samp class="option">udu_sei <var class="var">boolean</var></samp></dt>
<dd><p>Import user data unregistered SEI if available into output. Default is 0 (off).
</p>
</dd>
<dt><samp class="option">mb_info <var class="var">boolean</var></samp></dt>
<dd><p>Set mb_info data through AVFrameSideData, only useful when used from the
API. Default is 0 (off).
</p></dd>
</dl>

<p>Encoding ffpresets for common usages are provided so they can be used with the
general presets system (e.g. passing the <samp class="option">pre</samp> option).
</p>
</div>
</div>
<div class="section-level-extent" id="libx265">
<h3 class="section"><span>9.17 libx265<a class="copiable-link" href="#libx265"> &para;</a></span></h3>

<p>x265 H.265/HEVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libx265 headers and library
during configuration. You need to explicitly configure the build with
<samp class="option">--enable-libx265</samp>.
</p>
<ul class="mini-toc">
<li><a href="#Options-39" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-39">
<h4 class="subsection"><span>9.17.1 Options<a class="copiable-link" href="#Options-39"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Sets target video bitrate.
</p>
</dd>
<dt><samp class="option">bf</samp></dt>
<dt><samp class="option">g</samp></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><samp class="option">keyint_min</samp></dt>
<dd><p>Minimum GOP size.
</p>
</dd>
<dt><samp class="option">refs</samp></dt>
<dd><p>Number of reference frames each P-frame can use. The range is from <var class="var">1-16</var>.
</p>
</dd>
<dt><samp class="option">preset</samp></dt>
<dd><p>Set the x265 preset.
</p>
</dd>
<dt><samp class="option">tune</samp></dt>
<dd><p>Set the x265 tune parameter.
</p>
</dd>
<dt><samp class="option">profile</samp></dt>
<dd><p>Set profile restrictions.
</p>
</dd>
<dt><samp class="option">crf</samp></dt>
<dd><p>Set the quality for constant quality mode.
</p>
</dd>
<dt><samp class="option">qp</samp></dt>
<dd><p>Set constant quantization rate control method parameter.
</p>
</dd>
<dt><samp class="option">qmin</samp></dt>
<dd><p>Minimum quantizer scale.
</p>
</dd>
<dt><samp class="option">qmax</samp></dt>
<dd><p>Maximum quantizer scale.
</p>
</dd>
<dt><samp class="option">qdiff</samp></dt>
<dd><p>Maximum difference between quantizer scales.
</p>
</dd>
<dt><samp class="option">qblur</samp></dt>
<dd><p>Quantizer curve blur
</p>
</dd>
<dt><samp class="option">qcomp</samp></dt>
<dd><p>Quantizer curve compression factor
</p>
</dd>
<dt><samp class="option">i_qfactor</samp></dt>
<dt><samp class="option">b_qfactor</samp></dt>
<dt><samp class="option">forced-idr</samp></dt>
<dd><p>Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.
</p>
</dd>
<dt><samp class="option">udu_sei <var class="var">boolean</var></samp></dt>
<dd><p>Import user data unregistered SEI if available into output. Default is 0 (off).
</p>
</dd>
<dt><samp class="option">x265-params</samp></dt>
<dd><p>Set x265 options using a list of <var class="var">key</var>=<var class="var">value</var> couples separated
by &quot;:&quot;. See <code class="command">x265 --help</code> for a list of options.
</p>
<p>For example to specify libx265 encoding options with <samp class="option">-x265-params</samp>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input -c:v libx265 -x265-params crf=26:psy-rd=1 output.mp4
</pre></div>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libxavs2">
<h3 class="section"><span>9.18 libxavs2<a class="copiable-link" href="#libxavs2"> &para;</a></span></h3>

<p>xavs2 AVS2-P2/IEEE1857.4 encoder wrapper.
</p>
<p>This encoder requires the presence of the libxavs2 headers and library
during configuration. You need to explicitly configure the build with
<samp class="option">--enable-libxavs2</samp>.
</p>
<p>The following standard libavcodec options are used:
</p><ul class="itemize mark-bullet">
<li><samp class="option">b</samp> / <samp class="option">bit_rate</samp>
</li><li><samp class="option">g</samp> / <samp class="option">gop_size</samp>
</li><li><samp class="option">bf</samp> / <samp class="option">max_b_frames</samp>
</li></ul>

<p>The encoder also has its own specific options:
</p><ul class="mini-toc">
<li><a href="#Options-40" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-40">
<h4 class="subsection"><span>9.18.1 Options<a class="copiable-link" href="#Options-40"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">lcu_row_threads</samp></dt>
<dd><p>Set the number of parallel threads for rows from 1 to 8 (default 5).
</p>
</dd>
<dt><samp class="option">initial_qp</samp></dt>
<dd><p>Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the initial qp for the first frame.
</p>
</dd>
<dt><samp class="option">qp</samp></dt>
<dd><p>Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the qp value under constant-QP mode.
</p>
</dd>
<dt><samp class="option">max_qp</samp></dt>
<dd><p>Set the max qp for rate control from 1 to 63 (default 55).
</p>
</dd>
<dt><samp class="option">min_qp</samp></dt>
<dd><p>Set the min qp for rate control from 1 to 63 (default 20).
</p>
</dd>
<dt><samp class="option">speed_level</samp></dt>
<dd><p>Set the Speed level from 0 to 9 (default 0). Higher is better but slower.
</p>
</dd>
<dt><samp class="option">log_level</samp></dt>
<dd><p>Set the log level from -1 to 3 (default 0). -1: none, 0: error,
1: warning, 2: info, 3: debug.
</p>
</dd>
<dt><samp class="option">xavs2-params</samp></dt>
<dd><p>Set xavs2 options using a list of <var class="var">key</var>=<var class="var">value</var> couples separated
by &quot;:&quot;.
</p>
<p>For example to specify libxavs2 encoding options with <samp class="option">-xavs2-params</samp>:
</p>
<div class="example">
<pre class="example-preformatted">ffmpeg -i input -c:v libxavs2 -xavs2-params RdoqLevel=0 output.avs2
</pre></div>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libxeve">
<h3 class="section"><span>9.19 libxeve<a class="copiable-link" href="#libxeve"> &para;</a></span></h3>

<p>eXtra-fast Essential Video Encoder (XEVE) MPEG-5 EVC encoder wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.
</p>
<p>This encoder requires the presence of the libxeve headers and library
during configuration. You need to explicitly configure the build with
<samp class="option">--enable-libxeve</samp>.
</p>
<div class="info">
<p>Many libxeve encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.
Additionally the xeve-params private options allows one to pass a list
of key=value tuples as accepted by the libxeve <code class="code">parse_xeve_params</code> function.
</p></div>
<p>The xeve project website is at <a class="url" href="https://github.com/mpeg5/xeve">https://github.com/mpeg5/xeve</a>.
</p>
<ul class="mini-toc">
<li><a href="#Options-41" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-41">
<h4 class="subsection"><span>9.19.1 Options<a class="copiable-link" href="#Options-41"> &para;</a></span></h4>

<p>The following options are supported by the libxeve wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.
</p>
<div class="info">
<p>To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
<a class="ref" href="#codec_002doptions">the Codec Options chapter</a>.
</p></div>
<div class="info">
<p>To get a more accurate and extensive documentation of the libxeve options,
invoke the command  <code class="code">xeve_app --help</code> or consult the libxeve documentation.
</p></div>
<dl class="table">
<dt><samp class="option">b (<em class="emph">bitrate</em>)</samp></dt>
<dd><p>Set target video bitrate in bits/s.
Note that FFmpeg&rsquo;s b option is expressed in bits/s, while xeve&rsquo;s bitrate is in kilobits/s.
</p>
</dd>
<dt><samp class="option">bf (<em class="emph">bframes</em>)</samp></dt>
<dd><p>Set the maximum number of B frames (1,3,7,15).
</p>
</dd>
<dt><samp class="option">g (<em class="emph">keyint</em>)</samp></dt>
<dd><p>Set the GOP size (I-picture period).
</p>
</dd>
<dt><samp class="option">preset (<em class="emph">preset</em>)</samp></dt>
<dd><p>Set the xeve preset.
Set the encoder preset value to determine encoding speed [fast, medium, slow, placebo]
</p>
</dd>
<dt><samp class="option">tune (<em class="emph">tune</em>)</samp></dt>
<dd><p>Set the encoder tune parameter [psnr, zerolatency]
</p>
</dd>
<dt><samp class="option">profile (<em class="emph">profile</em>)</samp></dt>
<dd><p>Set the encoder profile [0: baseline; 1: main]
</p>
</dd>
<dt><samp class="option">crf (<em class="emph">crf</em>)</samp></dt>
<dd><p>Set the quality for constant quality mode.
Constant rate factor &lt;10..49&gt; [default: 32]
</p>
</dd>
<dt><samp class="option">qp (<em class="emph">qp</em>)</samp></dt>
<dd><p>Set constant quantization rate control method parameter.
Quantization parameter qp &lt;0..51&gt; [default: 32]
</p>
</dd>
<dt><samp class="option">threads (<em class="emph">threads</em>)</samp></dt>
<dd><p>Force to use a specific number of threads
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="libxvid">
<h3 class="section"><span>9.20 libxvid<a class="copiable-link" href="#libxvid"> &para;</a></span></h3>

<p>Xvid MPEG-4 Part 2 encoder wrapper.
</p>
<p>This encoder requires the presence of the libxvidcore headers and library
during configuration. You need to explicitly configure the build with
<code class="code">--enable-libxvid --enable-gpl</code>.
</p>
<p>The native <code class="code">mpeg4</code> encoder supports the MPEG-4 Part 2 format, so
users can encode to this format without this library.
</p>
<ul class="mini-toc">
<li><a href="#Options-42" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-42">
<h4 class="subsection"><span>9.20.1 Options<a class="copiable-link" href="#Options-42"> &para;</a></span></h4>

<p>The following options are supported by the libxvid wrapper. Some of
the following options are listed but are not documented, and
correspond to shared codec options. See <a class="ref" href="#codec_002doptions">the Codec
Options chapter</a> for their documentation. The other shared options
which are not listed have no effect for the libxvid encoder.
</p>
<dl class="table">
<dt><samp class="option">b</samp></dt>
<dt><samp class="option">g</samp></dt>
<dt><samp class="option">qmin</samp></dt>
<dt><samp class="option">qmax</samp></dt>
<dt><samp class="option">mpeg_quant</samp></dt>
<dt><samp class="option">threads</samp></dt>
<dt><samp class="option">bf</samp></dt>
<dt><samp class="option">b_qfactor</samp></dt>
<dt><samp class="option">b_qoffset</samp></dt>
<dt><samp class="option">flags</samp></dt>
<dd><p>Set specific encoding flags. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">mv4</samp>&rsquo;</dt>
<dd><p>Use four motion vector by macroblock.
</p>
</dd>
<dt>&lsquo;<samp class="samp">aic</samp>&rsquo;</dt>
<dd><p>Enable high quality AC prediction.
</p>
</dd>
<dt>&lsquo;<samp class="samp">gray</samp>&rsquo;</dt>
<dd><p>Only encode grayscale.
</p>
</dd>
<dt>&lsquo;<samp class="samp">qpel</samp>&rsquo;</dt>
<dd><p>Enable quarter-pixel motion compensation.
</p>
</dd>
<dt>&lsquo;<samp class="samp">cgop</samp>&rsquo;</dt>
<dd><p>Enable closed GOP.
</p>
</dd>
<dt>&lsquo;<samp class="samp">global_header</samp>&rsquo;</dt>
<dd><p>Place global headers in extradata instead of every keyframe.
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">gmc</samp></dt>
<dd><p>Enable the use of global motion compensation (GMC).  Default is 0
(disabled).
</p>
</dd>
<dt><samp class="option">me_quality</samp></dt>
<dd><p>Set motion estimation quality level. Possible values in decreasing order of
speed and increasing order of quality:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">0</samp>&rsquo;</dt>
<dd><p>Use no motion estimation (default).
</p>
</dd>
<dt>&lsquo;<samp class="samp">1, 2</samp>&rsquo;</dt>
<dd><p>Enable advanced diamond zonal search for 16x16 blocks and half-pixel
refinement for 16x16 blocks.
</p>
</dd>
<dt>&lsquo;<samp class="samp">3, 4</samp>&rsquo;</dt>
<dd><p>Enable all of the things described above, plus advanced diamond zonal
search for 8x8 blocks and half-pixel refinement for 8x8 blocks, also
enable motion estimation on chroma planes for P and B-frames.
</p>
</dd>
<dt>&lsquo;<samp class="samp">5, 6</samp>&rsquo;</dt>
<dd><p>Enable all of the things described above, plus extended 16x16 and 8x8
blocks search.
</p></dd>
</dl>

</dd>
<dt><samp class="option">mbd</samp></dt>
<dd><p>Set macroblock decision algorithm. Possible values in the increasing
order of quality:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
<dd><p>Use macroblock comparing function algorithm (default).
</p>
</dd>
<dt>&lsquo;<samp class="samp">bits</samp>&rsquo;</dt>
<dd><p>Enable rate distortion-based half pixel and quarter pixel refinement for
16x16 blocks.
</p>
</dd>
<dt>&lsquo;<samp class="samp">rd</samp>&rsquo;</dt>
<dd><p>Enable all of the things described above, plus rate distortion-based
half pixel and quarter pixel refinement for 8x8 blocks, and rate
distortion-based search using square pattern.
</p></dd>
</dl>

</dd>
<dt><samp class="option">lumi_aq</samp></dt>
<dd><p>Enable lumi masking adaptive quantization when set to 1. Default is 0
(disabled).
</p>
</dd>
<dt><samp class="option">variance_aq</samp></dt>
<dd><p>Enable variance adaptive quantization when set to 1. Default is 0
(disabled).
</p>
<p>When combined with <samp class="option">lumi_aq</samp>, the resulting quality will not
be better than any of the two specified individually. In other
words, the resulting quality will be the worse one of the two
effects.
</p>
</dd>
<dt><samp class="option">trellis</samp></dt>
<dd><p>Set rate-distortion optimal quantization.
</p>
</dd>
<dt><samp class="option">ssim</samp></dt>
<dd><p>Set structural similarity (SSIM) displaying method. Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">off</samp>&rsquo;</dt>
<dd><p>Disable displaying of SSIM information.
</p>
</dd>
<dt>&lsquo;<samp class="samp">avg</samp>&rsquo;</dt>
<dd><p>Output average SSIM at the end of encoding to stdout. The format of
showing the average SSIM is:
</p>
<div class="example">
<pre class="example-preformatted">Average SSIM: %f
</pre></div>

<p>For users who are not familiar with C, %f means a float number, or
a decimal (e.g. 0.939232).
</p>
</dd>
<dt>&lsquo;<samp class="samp">frame</samp>&rsquo;</dt>
<dd><p>Output both per-frame SSIM data during encoding and average SSIM at
the end of encoding to stdout. The format of per-frame information
is:
</p>
<div class="example">
<pre class="example-preformatted">       SSIM: avg: %1.3f min: %1.3f max: %1.3f
</pre></div>

<p>For users who are not familiar with C, %1.3f means a float number
rounded to 3 digits after the dot (e.g. 0.932).
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">ssim_acc</samp></dt>
<dd><p>Set SSIM accuracy. Valid options are integers within the range of
0-4, while 0 gives the most accurate result and 4 computes the
fastest.
</p>
</dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="MediaFoundation">
<h3 class="section"><span>9.21 MediaFoundation<a class="copiable-link" href="#MediaFoundation"> &para;</a></span></h3>

<p>This provides wrappers to encoders (both audio and video) in the
MediaFoundation framework. It can access both SW and HW encoders.
Video encoders can take input in either of nv12 or yuv420p form
(some encoders support both, some support only either - in practice,
nv12 is the safer choice, especially among HW encoders).
</p>
</div>
<div class="section-level-extent" id="Microsoft-RLE">
<h3 class="section"><span>9.22 Microsoft RLE<a class="copiable-link" href="#Microsoft-RLE"> &para;</a></span></h3>

<p>Microsoft RLE aka MSRLE encoder.
Only 8-bit palette mode supported.
Compatible with Windows 3.1 and Windows 95.
</p>
<ul class="mini-toc">
<li><a href="#Options-43" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-43">
<h4 class="subsection"><span>9.22.1 Options<a class="copiable-link" href="#Options-43"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">g <var class="var">integer</var></samp></dt>
<dd><p>Keyframe interval.
A keyframe is inserted at least every <code class="code">-g</code> frames, sometimes sooner.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="mpeg2">
<h3 class="section"><span>9.23 mpeg2<a class="copiable-link" href="#mpeg2"> &para;</a></span></h3>

<p>MPEG-2 video encoder.
</p>
<ul class="mini-toc">
<li><a href="#Options-44" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-44">
<h4 class="subsection"><span>9.23.1 Options<a class="copiable-link" href="#Options-44"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">profile</samp></dt>
<dd><p>Select the mpeg2 profile to encode:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">422</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">ss</samp>&rsquo;</dt>
<dd><p>Spatially Scalable
</p></dd>
<dt>&lsquo;<samp class="samp">snr</samp>&rsquo;</dt>
<dd><p>SNR Scalable
</p></dd>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">level</samp></dt>
<dd><p>Select the mpeg2 level to encode:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high1440</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">low</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">seq_disp_ext <var class="var">integer</var></samp></dt>
<dd><p>Specifies if the encoder should write a sequence_display_extension to the
output.
</p><dl class="table">
<dt><samp class="option">-1</samp></dt>
<dt><samp class="option">auto</samp></dt>
<dd><p>Decide automatically to write it or not (this is the default) by checking if
the data to be written is different from the default or unspecified values.
</p></dd>
<dt><samp class="option">0</samp></dt>
<dt><samp class="option">never</samp></dt>
<dd><p>Never write it.
</p></dd>
<dt><samp class="option">1</samp></dt>
<dt><samp class="option">always</samp></dt>
<dd><p>Always write it.
</p></dd>
</dl>
</dd>
<dt><samp class="option">video_format <var class="var">integer</var></samp></dt>
<dd><p>Specifies the video_format written into the sequence display extension
indicating the source of the video pictures. The default is &lsquo;<samp class="samp">unspecified</samp>&rsquo;,
can be &lsquo;<samp class="samp">component</samp>&rsquo;, &lsquo;<samp class="samp">pal</samp>&rsquo;, &lsquo;<samp class="samp">ntsc</samp>&rsquo;, &lsquo;<samp class="samp">secam</samp>&rsquo; or &lsquo;<samp class="samp">mac</samp>&rsquo;.
For maximum compatibility, use &lsquo;<samp class="samp">component</samp>&rsquo;.
</p></dd>
<dt><samp class="option">a53cc <var class="var">boolean</var></samp></dt>
<dd><p>Import closed captions (which must be ATSC compatible format) into output.
Default is 1 (on).
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="png">
<h3 class="section"><span>9.24 png<a class="copiable-link" href="#png"> &para;</a></span></h3>

<p>PNG image encoder.
</p>
<ul class="mini-toc">
<li><a href="#Private-options-1" accesskey="1">Private options</a></li>
</ul>
<div class="subsection-level-extent" id="Private-options-1">
<h4 class="subsection"><span>9.24.1 Private options<a class="copiable-link" href="#Private-options-1"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">dpi <var class="var">integer</var></samp></dt>
<dd><p>Set physical density of pixels, in dots per inch, unset by default
</p></dd>
<dt><samp class="option">dpm <var class="var">integer</var></samp></dt>
<dd><p>Set physical density of pixels, in dots per meter, unset by default
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="ProRes">
<h3 class="section"><span>9.25 ProRes<a class="copiable-link" href="#ProRes"> &para;</a></span></h3>

<p>Apple ProRes encoder.
</p>
<p>FFmpeg contains 2 ProRes encoders, the prores-aw and prores-ks encoder.
The used encoder can be chosen with the <code class="code">-vcodec</code> option.
</p>
<ul class="mini-toc">
<li><a href="#Private-Options-for-prores_002dks" accesskey="1">Private Options for prores-ks</a></li>
<li><a href="#Speed-considerations" accesskey="2">Speed considerations</a></li>
</ul>
<div class="subsection-level-extent" id="Private-Options-for-prores_002dks">
<h4 class="subsection"><span>9.25.1 Private Options for prores-ks<a class="copiable-link" href="#Private-Options-for-prores_002dks"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">profile <var class="var">integer</var></samp></dt>
<dd><p>Select the ProRes profile to encode
</p><dl class="table">
<dt>&lsquo;<samp class="samp">proxy</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">lt</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">standard</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hq</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">4444</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">4444xq</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option">quant_mat <var class="var">integer</var></samp></dt>
<dd><p>Select quantization matrix.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">default</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">proxy</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">lt</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">standard</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hq</samp>&rsquo;</dt>
</dl>
<p>If set to <var class="var">auto</var>, the matrix matching the profile will be picked.
If not set, the matrix providing the highest quality, <var class="var">default</var>, will be
picked.
</p>
</dd>
<dt><samp class="option">bits_per_mb <var class="var">integer</var></samp></dt>
<dd><p>How many bits to allot for coding one macroblock. Different profiles use
between 200 and 2400 bits per macroblock, the maximum is 8000.
</p>
</dd>
<dt><samp class="option">mbs_per_slice <var class="var">integer</var></samp></dt>
<dd><p>Number of macroblocks in each slice (1-8); the default value (8)
should be good in almost all situations.
</p>
</dd>
<dt><samp class="option">vendor <var class="var">string</var></samp></dt>
<dd><p>Override the 4-byte vendor ID.
A custom vendor ID like <var class="var">apl0</var> would claim the stream was produced by
the Apple encoder.
</p>
</dd>
<dt><samp class="option">alpha_bits <var class="var">integer</var></samp></dt>
<dd><p>Specify number of bits for alpha component.
Possible values are <var class="var">0</var>, <var class="var">8</var> and <var class="var">16</var>.
Use <var class="var">0</var> to disable alpha plane coding.
</p>
</dd>
</dl>

</div>
<div class="subsection-level-extent" id="Speed-considerations">
<h4 class="subsection"><span>9.25.2 Speed considerations<a class="copiable-link" href="#Speed-considerations"> &para;</a></span></h4>

<p>In the default mode of operation the encoder has to honor frame constraints
(i.e. not produce frames with size bigger than requested) while still making
output picture as good as possible.
A frame containing a lot of small details is harder to compress and the encoder
would spend more time searching for appropriate quantizers for each slice.
</p>
<p>Setting a higher <samp class="option">bits_per_mb</samp> limit will improve the speed.
</p>
<p>For the fastest encoding speed set the <samp class="option">qscale</samp> parameter (4 is the
recommended value) and do not set a size constraint.
</p>
</div>
</div>
<div class="section-level-extent" id="QSV-Encoders">
<h3 class="section"><span>9.26 QSV Encoders<a class="copiable-link" href="#QSV-Encoders"> &para;</a></span></h3>

<p>The family of Intel QuickSync Video encoders (MPEG-2, H.264, HEVC, JPEG/MJPEG,
VP9, AV1)
</p>
<ul class="mini-toc">
<li><a href="#Ratecontrol-Method" accesskey="1">Ratecontrol Method</a></li>
<li><a href="#Global-Options-_002d_003e-MSDK-Options" accesskey="2">Global Options -&gt; MSDK Options</a></li>
<li><a href="#Common-Options-1" accesskey="3">Common Options</a></li>
<li><a href="#Runtime-Options" accesskey="4">Runtime Options</a></li>
<li><a href="#H264-options" accesskey="5">H264 options</a></li>
<li><a href="#HEVC-Options-1" accesskey="6">HEVC Options</a></li>
<li><a href="#MPEG2-Options" accesskey="7">MPEG2 Options</a></li>
<li><a href="#VP9-Options" accesskey="8">VP9 Options</a></li>
<li><a href="#AV1-Options" accesskey="9">AV1 Options</a></li>
</ul>
<div class="subsection-level-extent" id="Ratecontrol-Method">
<h4 class="subsection"><span>9.26.1 Ratecontrol Method<a class="copiable-link" href="#Ratecontrol-Method"> &para;</a></span></h4>
<p>The ratecontrol method is selected as follows:
</p><ul class="itemize mark-bullet">
<li>When <samp class="option">global_quality</samp> is specified, a quality-based mode is used.
Specifically this means either
<ul class="itemize mark-minus">
<li><var class="var">CQP</var> - constant quantizer scale, when the <samp class="option">qscale</samp> codec flag is
also set (the <samp class="option">-qscale</samp> ffmpeg option).

</li><li><var class="var">LA_ICQ</var> - intelligent constant quality with lookahead, when the
<samp class="option">look_ahead</samp> option is also set.

</li><li><var class="var">ICQ</var> &ndash; intelligent constant quality otherwise. For the ICQ modes, global
quality range is 1 to 51, with 1 being the best quality.
</li></ul>

</li><li>Otherwise when the desired average bitrate is specified with the <samp class="option">b</samp>
option, a bitrate-based mode is used.
<ul class="itemize mark-minus">
<li><var class="var">LA</var> - VBR with lookahead, when the <samp class="option">look_ahead</samp> option is specified.

</li><li><var class="var">VCM</var> - video conferencing mode, when the <samp class="option">vcm</samp> option is set.

</li><li><var class="var">CBR</var> - constant bitrate, when <samp class="option">maxrate</samp> is specified and equal to
the average bitrate.

</li><li><var class="var">VBR</var> - variable bitrate, when <samp class="option">maxrate</samp> is specified, but is higher
than the average bitrate.

</li><li><var class="var">AVBR</var> - average VBR mode, when <samp class="option">maxrate</samp> is not specified, both
<samp class="option">avbr_accuracy</samp> and <samp class="option">avbr_convergence</samp> are set to non-zero. This
mode is available for H264 and HEVC on Windows.
</li></ul>

</li><li>Otherwise the default ratecontrol method <var class="var">CQP</var> is used.
</li></ul>

<p>Note that depending on your system, a different mode than the one you specified
may be selected by the encoder. Set the verbosity level to <var class="var">verbose</var> or
higher to see the actual settings used by the QSV runtime.
</p>
</div>
<div class="subsection-level-extent" id="Global-Options-_002d_003e-MSDK-Options">
<h4 class="subsection"><span>9.26.2 Global Options -&gt; MSDK Options<a class="copiable-link" href="#Global-Options-_002d_003e-MSDK-Options"> &para;</a></span></h4>
<p>Additional libavcodec global options are mapped to MSDK options as follows:
</p>
<ul class="itemize mark-bullet">
<li><samp class="option">g/gop_size</samp> -&gt; <samp class="option">GopPicSize</samp>

</li><li><samp class="option">bf/max_b_frames</samp>+1 -&gt; <samp class="option">GopRefDist</samp>

</li><li><samp class="option">rc_init_occupancy/rc_initial_buffer_occupancy</samp> -&gt;
<samp class="option">InitialDelayInKB</samp>

</li><li><samp class="option">slices</samp> -&gt; <samp class="option">NumSlice</samp>

</li><li><samp class="option">refs</samp> -&gt; <samp class="option">NumRefFrame</samp>

</li><li><samp class="option">b_strategy/b_frame_strategy</samp> -&gt; <samp class="option">BRefType</samp>

</li><li><samp class="option">cgop/CLOSED_GOP</samp> codec flag -&gt; <samp class="option">GopOptFlag</samp>

</li><li>For the <var class="var">CQP</var> mode, the <samp class="option">i_qfactor/i_qoffset</samp> and
<samp class="option">b_qfactor/b_qoffset</samp> set the difference between <var class="var">QPP</var> and <var class="var">QPI</var>,
and <var class="var">QPP</var> and <var class="var">QPB</var> respectively.

</li><li>Setting the <samp class="option">coder</samp> option to the value <var class="var">vlc</var> will make the H.264
encoder use CAVLC instead of CABAC.

</li></ul>

</div>
<div class="subsection-level-extent" id="Common-Options-1">
<h4 class="subsection"><span>9.26.3 Common Options<a class="copiable-link" href="#Common-Options-1"> &para;</a></span></h4>
<p>Following options are used by all qsv encoders.
</p>
<dl class="table">
<dt><samp class="option"><var class="var">async_depth</var></samp></dt>
<dd><p>Specifies how many asynchronous operations an application performs
before the application explicitly synchronizes the result. If zero,
the value is not specified.
</p>
</dd>
<dt><samp class="option"><var class="var">preset</var></samp></dt>
<dd><p>This option itemizes a range of choices from veryfast (best speed) to veryslow
(best quality).
</p><dl class="table">
<dt>&lsquo;<samp class="samp">veryfast</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">faster</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">fast</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">medium</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">slow</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">slower</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">veryslow</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">forced_idr</var></samp></dt>
<dd><p>Forcing I frames as IDR frames.
</p>
</dd>
<dt><samp class="option"><var class="var">low_power</var></samp></dt>
<dd><p>For encoders set this flag to ON to reduce power consumption and GPU usage.
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="Runtime-Options">
<h4 class="subsection"><span>9.26.4 Runtime Options<a class="copiable-link" href="#Runtime-Options"> &para;</a></span></h4>
<p>Following options can be used durning qsv encoding.
</p>
<dl class="table">
<dt><samp class="option"><var class="var">global_quality</var></samp></dt>
<dt><samp class="option"><var class="var">i_quant_factor</var></samp></dt>
<dt><samp class="option"><var class="var">i_quant_offset</var></samp></dt>
<dt><samp class="option"><var class="var">b_quant_factor</var></samp></dt>
<dt><samp class="option"><var class="var">b_quant_offset</var></samp></dt>
<dd><p>Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec&rsquo;s qp configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size</var></samp></dt>
<dd><p>Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec&rsquo;s MaxFrameSize configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">gop_size</var></samp></dt>
<dd><p>Change this value to reset qsv codec&rsquo;s gop configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_type</var></samp></dt>
<dt><samp class="option"><var class="var">int_ref_cycle_size</var></samp></dt>
<dt><samp class="option"><var class="var">int_ref_qp_delta</var></samp></dt>
<dt><samp class="option"><var class="var">int_ref_cycle_dist</var></samp></dt>
<dd><p>Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec&rsquo;s Intra Refresh configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">qmax</var></samp></dt>
<dt><samp class="option"><var class="var">qmin</var></samp></dt>
<dt><samp class="option"><var class="var">max_qp_i</var></samp></dt>
<dt><samp class="option"><var class="var">min_qp_i</var></samp></dt>
<dt><samp class="option"><var class="var">max_qp_p</var></samp></dt>
<dt><samp class="option"><var class="var">min_qp_p</var></samp></dt>
<dt><samp class="option"><var class="var">max_qp_b</var></samp></dt>
<dt><samp class="option"><var class="var">min_qp_b</var></samp></dt>
<dd><p>Supported in h264_qsv.
Change these value to reset qsv codec&rsquo;s max/min qp configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">low_delay_brc</var></samp></dt>
<dd><p>Supported in h264_qsv, hevc_qsv and av1_qsv.
Change this value to reset qsv codec&rsquo;s low_delay_brc configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">framerate</var></samp></dt>
<dd><p>Change this value to reset qsv codec&rsquo;s framerate configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">bit_rate</var></samp></dt>
<dt><samp class="option"><var class="var">rc_buffer_size</var></samp></dt>
<dt><samp class="option"><var class="var">rc_initial_buffer_occupancy</var></samp></dt>
<dt><samp class="option"><var class="var">rc_max_rate</var></samp></dt>
<dd><p>Change these value to reset qsv codec&rsquo;s bitrate control configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">pic_timing_sei</var></samp></dt>
<dd><p>Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec&rsquo;s pic_timing_sei configuration.
</p>
</dd>
<dt><samp class="option"><var class="var">qsv_params</var></samp></dt>
<dd><p>Set QSV encoder parameters as a colon-separated list of key-value pairs.
</p>
<p>The <samp class="option">qsv_params</samp> should be formatted as <code class="code">key1=value1:key2=value2:...</code>.
</p>
<p>These parameters are passed directly to the underlying Intel Quick Sync Video (QSV) encoder using the MFXSetParameter function.
</p>
<p>Example:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i input.mp4 -c:v h264_qsv -qsv_params &quot;CodingOption1=1:CodingOption2=2&quot; output.mp4
</pre></div>

<p>This option allows fine-grained control over various encoder-specific settings provided by the QSV encoder.
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="H264-options">
<h4 class="subsection"><span>9.26.5 H264 options<a class="copiable-link" href="#H264-options"> &para;</a></span></h4>
<p>These options are used by h264_qsv
</p>
<dl class="table">
<dt><samp class="option"><var class="var">extbrc</var></samp></dt>
<dd><p>Extended bitrate control.
</p>
</dd>
<dt><samp class="option"><var class="var">recovery_point_sei</var></samp></dt>
<dd><p>Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
</p>
</dd>
<dt><samp class="option"><var class="var">rdo</var></samp></dt>
<dd><p>Enable rate distortion optimization.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size</var></samp></dt>
<dd><p>Maximum encoded frame size in bytes.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_i</var></samp></dt>
<dd><p>Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_p</var></samp></dt>
<dd><p>Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><samp class="option"><var class="var">max_slice_size</var></samp></dt>
<dd><p>Maximum encoded slice size in bytes.
</p>
</dd>
<dt><samp class="option"><var class="var">bitrate_limit</var></samp></dt>
<dd><p>Toggle bitrate limitations.
Modifies bitrate to be in the range imposed by the QSV encoder. Setting this
flag off may lead to violation of HRD conformance. Mind that specifying bitrate
below the QSV encoder range might significantly affect quality. If on this
option takes effect in non CQP modes: if bitrate is not in the range imposed
by the QSV encoder, it will be changed to be in the range.
</p>
</dd>
<dt><samp class="option"><var class="var">mbbrc</var></samp></dt>
<dd><p>Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
</p>
</dd>
<dt><samp class="option"><var class="var">low_delay_brc</var></samp></dt>
<dd><p>Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_i</var></samp></dt>
<dd><p>This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_b</var></samp></dt>
<dd><p>This flag controls changing of frame type from B to P.
</p>
</dd>
<dt><samp class="option"><var class="var">p_strategy</var></samp></dt>
<dd><p>Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).
</p>
</dd>
<dt><samp class="option"><var class="var">b_strategy</var></samp></dt>
<dd><p>This option controls usage of B frames as reference.
</p>
</dd>
<dt><samp class="option"><var class="var">dblk_idc</var></samp></dt>
<dd><p>This option disable deblocking. It has value in range 0~2.
</p>
</dd>
<dt><samp class="option"><var class="var">cavlc</var></samp></dt>
<dd><p>If set, CAVLC is used; if unset, CABAC is used for encoding.
</p>
</dd>
<dt><samp class="option"><var class="var">vcm</var></samp></dt>
<dd><p>Video conferencing mode, please see ratecontrol method.
</p>
</dd>
<dt><samp class="option"><var class="var">idr_interval</var></samp></dt>
<dd><p>Distance (in I-frames) between IDR frames.
</p>
</dd>
<dt><samp class="option"><var class="var">pic_timing_sei</var></samp></dt>
<dd><p>Insert picture timing SEI with pic_struct_syntax element.
</p>
</dd>
<dt><samp class="option"><var class="var">single_sei_nal_unit</var></samp></dt>
<dd><p>Put all the SEI messages into one NALU.
</p>
</dd>
<dt><samp class="option"><var class="var">max_dec_frame_buffering</var></samp></dt>
<dd><p>Maximum number of frames buffered in the DPB.
</p>
</dd>
<dt><samp class="option"><var class="var">look_ahead</var></samp></dt>
<dd><p>Use VBR algorithm with look ahead.
</p>
</dd>
<dt><samp class="option"><var class="var">look_ahead_depth</var></samp></dt>
<dd><p>Depth of look ahead in number frames.
</p>
</dd>
<dt><samp class="option"><var class="var">look_ahead_downsampling</var></samp></dt>
<dd><p>Downscaling factor for the frames saved for the lookahead analysis.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">off</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">2x</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">4x</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">int_ref_type</var></samp></dt>
<dd><p>Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. <var class="var">none</var> means no refresh. <var class="var">vertical</var> means
vertical refresh, by column of MBs. <var class="var">horizontal</var> means horizontal refresh,
by rows of MBs. <var class="var">slice</var> means horizontal refresh by slices without
overlapping. In case of <var class="var">slice</var>, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_cycle_size</var></samp></dt>
<dd><p>Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_qp_delta</var></samp></dt>
<dd><p>Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_cycle_dist</var></samp></dt>
<dd><p>Distance between the beginnings of the intra-refresh cycles in frames.
</p>
</dd>
<dt><samp class="option"><var class="var">profile</var></samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">baseline</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">a53cc</var></samp></dt>
<dd><p>Use A53 Closed Captions (if available).
</p>
</dd>
<dt><samp class="option"><var class="var">aud</var></samp></dt>
<dd><p>Insert the Access Unit Delimiter NAL.
</p>
</dd>
<dt><samp class="option"><var class="var">mfmode</var></samp></dt>
<dd><p>Multi-Frame Mode.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">off</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">auto</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">repeat_pps</var></samp></dt>
<dd><p>Repeat pps for every frame.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_i</var></samp></dt>
<dd><p>Maximum video quantizer scale for I frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_i</var></samp></dt>
<dd><p>Minimum video quantizer scale for I frame.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_p</var></samp></dt>
<dd><p>Maximum video quantizer scale for P frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_p</var></samp></dt>
<dd><p>Minimum video quantizer scale for P frame.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_b</var></samp></dt>
<dd><p>Maximum video quantizer scale for B frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_b</var></samp></dt>
<dd><p>Minimum video quantizer scale for B frame.
</p>
</dd>
<dt><samp class="option"><var class="var">scenario</var></samp></dt>
<dd><p>Provides a hint to encoder about the scenario for the encoding session.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">displayremoting</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">videoconference</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">archive</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">livestreaming</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cameracapture</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">videosurveillance</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">gamestreaming</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">remotegaming</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">avbr_accuracy</var></samp></dt>
<dd><p>Accuracy of the AVBR ratecontrol (unit of tenth of percent).
</p>
</dd>
<dt><samp class="option"><var class="var">avbr_convergence</var></samp></dt>
<dd><p>Convergence of the AVBR ratecontrol (unit of 100 frames)
</p>
<p>The parameters <var class="var">avbr_accuracy</var> and <var class="var">avbr_convergence</var> are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, <var class="var">target_bitrate</var>, within the accuracy range <var class="var">avbr_accuracy</var>,
after a <var class="var">avbr_Convergence</var> period. This method does not follow HRD and the
instant bitrate is not capped or padded.
</p>
</dd>
<dt><samp class="option"><var class="var">skip_frame</var></samp></dt>
<dd><p>Use per-frame metadata &quot;qsv_skip_frame&quot; to skip frame when encoding. This option
defines the usage of this metadata.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">no_skip</samp>&rsquo;</dt>
<dd><p>Frame skipping is disabled.
</p></dd>
<dt>&lsquo;<samp class="samp">insert_dummy</samp>&rsquo;</dt>
<dd><p>Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
</p></dd>
<dt>&lsquo;<samp class="samp">insert_nothing</samp>&rsquo;</dt>
<dd><p>Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
</p></dd>
<dt>&lsquo;<samp class="samp">brc_only</samp>&rsquo;</dt>
<dd><p>skip_frame metadata indicates the number of missed frames before the current
frame.
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsection-level-extent" id="HEVC-Options-1">
<h4 class="subsection"><span>9.26.6 HEVC Options<a class="copiable-link" href="#HEVC-Options-1"> &para;</a></span></h4>
<p>These options are used by hevc_qsv
</p>
<dl class="table">
<dt><samp class="option"><var class="var">extbrc</var></samp></dt>
<dd><p>Extended bitrate control.
</p>
</dd>
<dt><samp class="option"><var class="var">recovery_point_sei</var></samp></dt>
<dd><p>Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
</p>
</dd>
<dt><samp class="option"><var class="var">rdo</var></samp></dt>
<dd><p>Enable rate distortion optimization.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size</var></samp></dt>
<dd><p>Maximum encoded frame size in bytes.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_i</var></samp></dt>
<dd><p>Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_p</var></samp></dt>
<dd><p>Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><samp class="option"><var class="var">max_slice_size</var></samp></dt>
<dd><p>Maximum encoded slice size in bytes.
</p>
</dd>
<dt><samp class="option"><var class="var">mbbrc</var></samp></dt>
<dd><p>Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
</p>
</dd>
<dt><samp class="option"><var class="var">low_delay_brc</var></samp></dt>
<dd><p>Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_i</var></samp></dt>
<dd><p>This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_b</var></samp></dt>
<dd><p>This flag controls changing of frame type from B to P.
</p>
</dd>
<dt><samp class="option"><var class="var">p_strategy</var></samp></dt>
<dd><p>Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).
</p>
</dd>
<dt><samp class="option"><var class="var">b_strategy</var></samp></dt>
<dd><p>This option controls usage of B frames as reference.
</p>
</dd>
<dt><samp class="option"><var class="var">dblk_idc</var></samp></dt>
<dd><p>This option disable deblocking. It has value in range 0~2.
</p>
</dd>
<dt><samp class="option"><var class="var">idr_interval</var></samp></dt>
<dd><p>Distance (in I-frames) between IDR frames.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">begin_only</samp>&rsquo;</dt>
<dd><p>Output an IDR-frame only at the beginning of the stream.
</p></dd>
</dl>

</dd>
<dt><samp class="option"><var class="var">load_plugin</var></samp></dt>
<dd><p>A user plugin to load in an internal session.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">none</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hevc_sw</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">hevc_hw</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">load_plugins</var></samp></dt>
<dd><p>A :-separate list of hexadecimal plugin UIDs to load in
an internal session.
</p>
</dd>
<dt><samp class="option"><var class="var">look_ahead_depth</var></samp></dt>
<dd><p>Depth of look ahead in number frames, available when extbrc option is enabled.
</p>
</dd>
<dt><samp class="option"><var class="var">profile</var></samp></dt>
<dd><p>Set the encoding profile (scc requires libmfx &gt;= 1.32).
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main10</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">mainsp</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">rext</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">scc</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">tier</var></samp></dt>
<dd><p>Set the encoding tier (only level &gt;= 4 can support high tier).
This option only takes effect when the level option is specified.
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">gpb</var></samp></dt>
<dd><p>1: GPB (generalized P/B frame)
</p>
<p>0: regular P frame.
</p>
</dd>
<dt><samp class="option"><var class="var">tile_cols</var></samp></dt>
<dd><p>Number of columns for tiled encoding.
</p>
</dd>
<dt><samp class="option"><var class="var">tile_rows</var></samp></dt>
<dd><p>Number of rows for tiled encoding.
</p>
</dd>
<dt><samp class="option"><var class="var">aud</var></samp></dt>
<dd><p>Insert the Access Unit Delimiter NAL.
</p>
</dd>
<dt><samp class="option"><var class="var">pic_timing_sei</var></samp></dt>
<dd><p>Insert picture timing SEI with pic_struct_syntax element.
</p>
</dd>
<dt><samp class="option"><var class="var">transform_skip</var></samp></dt>
<dd><p>Turn this option ON to enable transformskip. It is supported on platform equal
or newer than ICL.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_type</var></samp></dt>
<dd><p>Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. <var class="var">none</var> means no refresh. <var class="var">vertical</var> means
vertical refresh, by column of MBs. <var class="var">horizontal</var> means horizontal refresh,
by rows of MBs. <var class="var">slice</var> means horizontal refresh by slices without
overlapping. In case of <var class="var">slice</var>, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_cycle_size</var></samp></dt>
<dd><p>Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_qp_delta</var></samp></dt>
<dd><p>Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.
</p>
</dd>
<dt><samp class="option"><var class="var">int_ref_cycle_dist</var></samp></dt>
<dd><p>Distance between the beginnings of the intra-refresh cycles in frames.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_i</var></samp></dt>
<dd><p>Maximum video quantizer scale for I frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_i</var></samp></dt>
<dd><p>Minimum video quantizer scale for I frame.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_p</var></samp></dt>
<dd><p>Maximum video quantizer scale for P frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_p</var></samp></dt>
<dd><p>Minimum video quantizer scale for P frame.
</p>
</dd>
<dt><samp class="option"><var class="var">max_qp_b</var></samp></dt>
<dd><p>Maximum video quantizer scale for B frame.
</p>
</dd>
<dt><samp class="option"><var class="var">min_qp_b</var></samp></dt>
<dd><p>Minimum video quantizer scale for B frame.
</p>
</dd>
<dt><samp class="option"><var class="var">scenario</var></samp></dt>
<dd><p>Provides a hint to encoder about the scenario for the encoding session.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">displayremoting</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">videoconference</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">archive</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">livestreaming</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cameracapture</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">videosurveillance</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">gamestreaming</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">remotegaming</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">avbr_accuracy</var></samp></dt>
<dd><p>Accuracy of the AVBR ratecontrol (unit of tenth of percent).
</p>
</dd>
<dt><samp class="option"><var class="var">avbr_convergence</var></samp></dt>
<dd><p>Convergence of the AVBR ratecontrol (unit of 100 frames)
</p>
<p>The parameters <var class="var">avbr_accuracy</var> and <var class="var">avbr_convergence</var> are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, <var class="var">target_bitrate</var>, within the accuracy range <var class="var">avbr_accuracy</var>,
after a <var class="var">avbr_Convergence</var> period. This method does not follow HRD and the
instant bitrate is not capped or padded.
</p>
</dd>
<dt><samp class="option"><var class="var">skip_frame</var></samp></dt>
<dd><p>Use per-frame metadata &quot;qsv_skip_frame&quot; to skip frame when encoding. This option
defines the usage of this metadata.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">no_skip</samp>&rsquo;</dt>
<dd><p>Frame skipping is disabled.
</p></dd>
<dt>&lsquo;<samp class="samp">insert_dummy</samp>&rsquo;</dt>
<dd><p>Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
</p></dd>
<dt>&lsquo;<samp class="samp">insert_nothing</samp>&rsquo;</dt>
<dd><p>Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
</p></dd>
<dt>&lsquo;<samp class="samp">brc_only</samp>&rsquo;</dt>
<dd><p>skip_frame metadata indicates the number of missed frames before the current
frame.
</p></dd>
</dl>

</dd>
</dl>

</div>
<div class="subsection-level-extent" id="MPEG2-Options">
<h4 class="subsection"><span>9.26.7 MPEG2 Options<a class="copiable-link" href="#MPEG2-Options"> &para;</a></span></h4>
<p>These options are used by mpeg2_qsv
</p><dl class="table">
<dt><samp class="option"><var class="var">profile</var></samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">simple</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">high</samp>&rsquo;</dt>
</dl>
</dd>
</dl>

</div>
<div class="subsection-level-extent" id="VP9-Options">
<h4 class="subsection"><span>9.26.8 VP9 Options<a class="copiable-link" href="#VP9-Options"> &para;</a></span></h4>
<p>These options are used by vp9_qsv
</p><dl class="table">
<dt><samp class="option"><var class="var">profile</var></samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">profile0</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">profile1</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">profile2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">profile3</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">tile_cols</var></samp></dt>
<dd><p>Number of columns for tiled encoding (requires libmfx &gt;= 1.29).
</p>
</dd>
<dt><samp class="option"><var class="var">tile_rows</var></samp></dt>
<dd><p>Number of rows for tiled encoding (requires libmfx  &gt;= 1.29).
</p></dd>
</dl>

</div>
<div class="subsection-level-extent" id="AV1-Options">
<h4 class="subsection"><span>9.26.9 AV1 Options<a class="copiable-link" href="#AV1-Options"> &para;</a></span></h4>
<p>These options are used by av1_qsv (requires libvpl).
</p><dl class="table">
<dt><samp class="option"><var class="var">profile</var></samp></dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">unknown</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">main</samp>&rsquo;</dt>
</dl>

</dd>
<dt><samp class="option"><var class="var">tile_cols</var></samp></dt>
<dd><p>Number of columns for tiled encoding.
</p>
</dd>
<dt><samp class="option"><var class="var">tile_rows</var></samp></dt>
<dd><p>Number of rows for tiled encoding.
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_i</var></samp></dt>
<dd><p>This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
</p>
</dd>
<dt><samp class="option"><var class="var">adaptive_b</var></samp></dt>
<dd><p>This flag controls changing of frame type from B to P.
</p>
</dd>
<dt><samp class="option"><var class="var">b_strategy</var></samp></dt>
<dd><p>This option controls usage of B frames as reference.
</p>
</dd>
<dt><samp class="option"><var class="var">extbrc</var></samp></dt>
<dd><p>Extended bitrate control.
</p>
</dd>
<dt><samp class="option"><var class="var">look_ahead_depth</var></samp></dt>
<dd><p>Depth of look ahead in number frames, available when extbrc option is enabled.
</p>
</dd>
<dt><samp class="option"><var class="var">low_delay_brc</var></samp></dt>
<dd><p>Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size</var></samp></dt>
<dd><p>Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_i</var></samp></dt>
<dd><p>Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><samp class="option"><var class="var">max_frame_size_p</var></samp></dt>
<dd><p>Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="snow">
<h3 class="section"><span>9.27 snow<a class="copiable-link" href="#snow"> &para;</a></span></h3>

<ul class="mini-toc">
<li><a href="#Options-45" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-45">
<h4 class="subsection"><span>9.27.1 Options<a class="copiable-link" href="#Options-45"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">iterative_dia_size</samp></dt>
<dd><p>dia size for the iterative motion estimation
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="VAAPI-encoders">
<h3 class="section"><span>9.28 VAAPI encoders<a class="copiable-link" href="#VAAPI-encoders"> &para;</a></span></h3>

<p>Wrappers for hardware encoders accessible via VAAPI.
</p>
<p>These encoders only accept input in VAAPI hardware surfaces.  If you have input
in software frames, use the <samp class="option">hwupload</samp> filter to upload them to the GPU.
</p>
<p>The following standard libavcodec options are used:
</p><ul class="itemize mark-bullet">
<li><samp class="option">g</samp> / <samp class="option">gop_size</samp>
</li><li><samp class="option">bf</samp> / <samp class="option">max_b_frames</samp>
</li><li><samp class="option">profile</samp>

<p>If not set, this will be determined automatically from the format of the input
frames and the profiles supported by the driver.
</p></li><li><samp class="option">level</samp>
</li><li><samp class="option">b</samp> / <samp class="option">bit_rate</samp>
</li><li><samp class="option">maxrate</samp> / <samp class="option">rc_max_rate</samp>
</li><li><samp class="option">bufsize</samp> / <samp class="option">rc_buffer_size</samp>
</li><li><samp class="option">rc_init_occupancy</samp> / <samp class="option">rc_initial_buffer_occupancy</samp>
</li><li><samp class="option">compression_level</samp>

<p>Speed / quality tradeoff: higher values are faster / worse quality.
</p></li><li><samp class="option">q</samp> / <samp class="option">global_quality</samp>

<p>Size / quality tradeoff: higher values are smaller / worse quality.
</p></li><li><samp class="option">qmin</samp>
</li><li><samp class="option">qmax</samp>
</li><li><samp class="option">i_qfactor</samp> / <samp class="option">i_quant_factor</samp>
</li><li><samp class="option">i_qoffset</samp> / <samp class="option">i_quant_offset</samp>
</li><li><samp class="option">b_qfactor</samp> / <samp class="option">b_quant_factor</samp>
</li><li><samp class="option">b_qoffset</samp> / <samp class="option">b_quant_offset</samp>
</li><li><samp class="option">slices</samp>
</li></ul>

<p>All encoders support the following options:
</p><dl class="table">
<dt><samp class="option">low_power</samp></dt>
<dd><p>Some drivers/platforms offer a second encoder for some codecs intended to use
less power than the default encoder; setting this option will attempt to use
that encoder.  Note that it may support a reduced feature set, so some other
options may not be available in this mode.
</p>
</dd>
<dt><samp class="option">idr_interval</samp></dt>
<dd><p>Set the number of normal intra frames between full-refresh (IDR) frames in
open-GOP mode.  The intra frames are still IRAPs, but will not include global
headers and may have non-decodable leading pictures.
</p>
</dd>
<dt><samp class="option">b_depth</samp></dt>
<dd><p>Set the B-frame reference depth.  When set to one (the default), all B-frames
will refer only to P- or I-frames.  When set to greater values multiple layers
of B-frames will be present, frames in each layer only referring to frames in
higher layers.
</p>
</dd>
<dt><samp class="option">async_depth</samp></dt>
<dd><p>Maximum processing parallelism. Increase this to improve single channel
performance. This option doesn&rsquo;t work if driver doesn&rsquo;t implement vaSyncBuffer
function. Please make sure there are enough hw_frames allocated if a large
number of async_depth is used.
</p>
</dd>
<dt><samp class="option">max_frame_size</samp></dt>
<dd><p>Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.
</p>
</dd>
<dt><samp class="option">rc_mode</samp></dt>
<dd><p>Set the rate control mode to use.  A given driver may only support a subset of
modes.
</p>
<p>Possible modes:
</p><dl class="table">
<dt><samp class="option">auto</samp></dt>
<dd><p>Choose the mode automatically based on driver support and the other options.
This is the default.
</p></dd>
<dt><samp class="option">CQP</samp></dt>
<dd><p>Constant-quality.
</p></dd>
<dt><samp class="option">CBR</samp></dt>
<dd><p>Constant-bitrate.
</p></dd>
<dt><samp class="option">VBR</samp></dt>
<dd><p>Variable-bitrate.
</p></dd>
<dt><samp class="option">ICQ</samp></dt>
<dd><p>Intelligent constant-quality.
</p></dd>
<dt><samp class="option">QVBR</samp></dt>
<dd><p>Quality-defined variable-bitrate.
</p></dd>
<dt><samp class="option">AVBR</samp></dt>
<dd><p>Average variable bitrate.
</p></dd>
</dl>

</dd>
<dt><samp class="option">blbrc</samp></dt>
<dd><p>Enable block level rate control, which assigns different bitrate block by block.
Invalid for CQP mode.
</p>
</dd>
</dl>

<p>Each encoder also has its own specific options:
</p><dl class="table">
<dt><samp class="option">av1_vaapi</samp></dt>
<dd><p><samp class="option">profile</samp> sets the value of <em class="emph">seq_profile</em>.
<samp class="option">tier</samp> sets the value of <em class="emph">seq_tier</em>.
<samp class="option">level</samp> sets the value of <em class="emph">seq_level_idx</em>.
</p>
<dl class="table">
<dt><samp class="option">tiles</samp></dt>
<dd><p>Set the number of tiles to encode the input video with, as columns x rows.
(default is auto, which means use minimal tile column/row number).
</p></dd>
<dt><samp class="option">tile_groups</samp></dt>
<dd><p>Set tile groups number. All the tiles will be distributed as evenly as possible to
each tile group. (default is 1).
</p></dd>
</dl>

</dd>
<dt><samp class="option">h264_vaapi</samp></dt>
<dd><p><samp class="option">profile</samp> sets the value of <em class="emph">profile_idc</em> and the <em class="emph">constraint_set*_flag</em>s.
<samp class="option">level</samp> sets the value of <em class="emph">level_idc</em>.
</p>
<dl class="table">
<dt><samp class="option">coder</samp></dt>
<dd><p>Set entropy encoder (default is <em class="emph">cabac</em>).  Possible values:
</p>
<dl class="table">
<dt>&lsquo;<samp class="samp">ac</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cabac</samp>&rsquo;</dt>
<dd><p>Use CABAC.
</p>
</dd>
<dt>&lsquo;<samp class="samp">vlc</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cavlc</samp>&rsquo;</dt>
<dd><p>Use CAVLC.
</p></dd>
</dl>

</dd>
<dt><samp class="option">aud</samp></dt>
<dd><p>Include access unit delimiters in the stream (not included by default).
</p>
</dd>
<dt><samp class="option">sei</samp></dt>
<dd><p>Set SEI message types to include.
Some combination of the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">identifier</samp>&rsquo;</dt>
<dd><p>Include a <em class="emph">user_data_unregistered</em> message containing information about
the encoder.
</p></dd>
<dt>&lsquo;<samp class="samp">timing</samp>&rsquo;</dt>
<dd><p>Include picture timing parameters (<em class="emph">buffering_period</em> and
<em class="emph">pic_timing</em> messages).
</p></dd>
<dt>&lsquo;<samp class="samp">recovery_point</samp>&rsquo;</dt>
<dd><p>Include recovery points where appropriate (<em class="emph">recovery_point</em> messages).
</p></dd>
</dl>

</dd>
</dl>

</dd>
<dt><samp class="option">hevc_vaapi</samp></dt>
<dd><p><samp class="option">profile</samp> and <samp class="option">level</samp> set the values of
<em class="emph">general_profile_idc</em> and <em class="emph">general_level_idc</em> respectively.
</p>
<dl class="table">
<dt><samp class="option">aud</samp></dt>
<dd><p>Include access unit delimiters in the stream (not included by default).
</p>
</dd>
<dt><samp class="option">tier</samp></dt>
<dd><p>Set <em class="emph">general_tier_flag</em>.  This may affect the level chosen for the stream
if it is not explicitly specified.
</p>
</dd>
<dt><samp class="option">sei</samp></dt>
<dd><p>Set SEI message types to include.
Some combination of the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">hdr</samp>&rsquo;</dt>
<dd><p>Include HDR metadata if the input frames have it
(<em class="emph">mastering_display_colour_volume</em> and <em class="emph">content_light_level</em>
messages).
</p></dd>
</dl>

</dd>
<dt><samp class="option">tiles</samp></dt>
<dd><p>Set the number of tiles to encode the input video with, as columns x rows.
Larger numbers allow greater parallelism in both encoding and decoding, but
may decrease coding efficiency.
</p>
</dd>
</dl>

</dd>
<dt><samp class="option">mjpeg_vaapi</samp></dt>
<dd><p>Only baseline DCT encoding is supported.  The encoder always uses the standard
quantisation and huffman tables - <samp class="option">global_quality</samp> scales the standard
quantisation table (range 1-100).
</p>
<p>For YUV, 4:2:0, 4:2:2 and 4:4:4 subsampling modes are supported.  RGB is also
supported, and will create an RGB JPEG.
</p>
<dl class="table">
<dt><samp class="option">jfif</samp></dt>
<dd><p>Include JFIF header in each frame (not included by default).
</p></dd>
<dt><samp class="option">huffman</samp></dt>
<dd><p>Include standard huffman tables (on by default).  Turning this off will save
a few hundred bytes in each output frame, but may lose compatibility with some
JPEG decoders which don&rsquo;t fully handle MJPEG.
</p></dd>
</dl>

</dd>
<dt><samp class="option">mpeg2_vaapi</samp></dt>
<dd><p><samp class="option">profile</samp> and <samp class="option">level</samp> set the value of <em class="emph">profile_and_level_indication</em>.
</p>
</dd>
<dt><samp class="option">vp8_vaapi</samp></dt>
<dd><p>B-frames are not supported.
</p>
<p><samp class="option">global_quality</samp> sets the <em class="emph">q_idx</em> used for non-key frames (range 0-127).
</p>
<dl class="table">
<dt><samp class="option">loop_filter_level</samp></dt>
<dt><samp class="option">loop_filter_sharpness</samp></dt>
<dd><p>Manually set the loop filter parameters.
</p></dd>
</dl>

</dd>
<dt><samp class="option">vp9_vaapi</samp></dt>
<dd><p><samp class="option">global_quality</samp> sets the <em class="emph">q_idx</em> used for P-frames (range 0-255).
</p>
<dl class="table">
<dt><samp class="option">loop_filter_level</samp></dt>
<dt><samp class="option">loop_filter_sharpness</samp></dt>
<dd><p>Manually set the loop filter parameters.
</p></dd>
</dl>

<p>B-frames are supported, but the output stream is always in encode order rather than display
order.  If B-frames are enabled, it may be necessary to use the <samp class="option">vp9_raw_reorder</samp>
bitstream filter to modify the output stream to display frames in the correct order.
</p>
<p>Only normal frames are produced - the <samp class="option">vp9_superframe</samp> bitstream filter may be
required to produce a stream usable with all decoders.
</p>
</dd>
</dl>

</div>
<div class="section-level-extent" id="vbn">
<h3 class="section"><span>9.29 vbn<a class="copiable-link" href="#vbn"> &para;</a></span></h3>

<p>Vizrt Binary Image encoder.
</p>
<p>This format is used by the broadcast vendor Vizrt for quick texture streaming.
Advanced features of the format such as LZW compression of texture data or
generation of mipmaps are not supported.
</p>
<ul class="mini-toc">
<li><a href="#Options-46" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-46">
<h4 class="subsection"><span>9.29.1 Options<a class="copiable-link" href="#Options-46"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">format <var class="var">string</var></samp></dt>
<dd><p>Sets the texture compression used by the VBN file. Can be <var class="var">dxt1</var>,
<var class="var">dxt5</var> or <var class="var">raw</var>. Default is <var class="var">dxt5</var>.
</p></dd>
</dl>

</div>
</div>
<div class="section-level-extent" id="vc2">
<h3 class="section"><span>9.30 vc2<a class="copiable-link" href="#vc2"> &para;</a></span></h3>

<p>SMPTE VC-2 (previously BBC Dirac Pro). This codec was primarily aimed at
professional broadcasting but since it supports yuv420, yuv422 and yuv444 at
8 (limited range or full range), 10 or 12 bits, this makes it suitable for
other tasks which require low overhead and low compression (like screen
recording).
</p>
<ul class="mini-toc">
<li><a href="#Options-47" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-47">
<h4 class="subsection"><span>9.30.1 Options<a class="copiable-link" href="#Options-47"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">b</samp></dt>
<dd><p>Sets target video bitrate. Usually that&rsquo;s around 1:6 of the uncompressed
video bitrate (e.g. for 1920x1080 50fps yuv422p10 that&rsquo;s around 400Mbps). Higher
values (close to the uncompressed bitrate) turn on lossless compression mode.
</p>
</dd>
<dt><samp class="option">field_order</samp></dt>
<dd><p>Enables field coding when set (e.g. to tt - top field first) for interlaced
inputs. Should increase compression with interlaced content as it splits the
fields and encodes each separately.
</p>
</dd>
<dt><samp class="option">wavelet_depth</samp></dt>
<dd><p>Sets the total amount of wavelet transforms to apply, between 1 and 5 (default).
Lower values reduce compression and quality. Less capable decoders may not be
able to handle values of <samp class="option">wavelet_depth</samp> over 3.
</p>
</dd>
<dt><samp class="option">wavelet_type</samp></dt>
<dd><p>Sets the transform type. Currently only <var class="var">5_3</var> (LeGall) and <var class="var">9_7</var>
(Deslauriers-Dubuc)
are implemented, with 9_7 being the one with better compression and thus
is the default.
</p>
</dd>
<dt><samp class="option">slice_width</samp></dt>
<dt><samp class="option">slice_height</samp></dt>
<dd><p>Sets the slice size for each slice. Larger values result in better compression.
For compatibility with other more limited decoders use <samp class="option">slice_width</samp> of
32 and <samp class="option">slice_height</samp> of 8.
</p>
</dd>
<dt><samp class="option">tolerance</samp></dt>
<dd><p>Sets the undershoot tolerance of the rate control system in percent. This is
to prevent an expensive search from being run.
</p>
</dd>
<dt><samp class="option">qm</samp></dt>
<dd><p>Sets the quantization matrix preset to use by default or when <samp class="option">wavelet_depth</samp>
is set to 5
</p><ul class="itemize mark-minus">
<li><var class="var">default</var>
Uses the default quantization matrix from the specifications, extended with
values for the fifth level. This provides a good balance between keeping detail
and omitting artifacts.

</li><li><var class="var">flat</var>
Use a completely zeroed out quantization matrix. This increases PSNR but might
reduce perception. Use in bogus benchmarks.

</li><li><var class="var">color</var>
Reduces detail but attempts to preserve color at extremely low bitrates.
</li></ul>

</dd>
</dl>


</div>
</div>
</div>
<div class="chapter-level-extent" id="Subtitles-Encoders">
<h2 class="chapter"><span>10 Subtitles Encoders<a class="copiable-link" href="#Subtitles-Encoders"> &para;</a></span></h2>

<ul class="mini-toc">
<li><a href="#dvdsub-1" accesskey="1">dvdsub</a></li>
</ul>
<div class="section-level-extent" id="dvdsub-1">
<h3 class="section"><span>10.1 dvdsub<a class="copiable-link" href="#dvdsub-1"> &para;</a></span></h3>

<p>This codec encodes the bitmap subtitle format that is used in DVDs.
Typically they are stored in VOBSUB file pairs (*.idx + *.sub),
and they can also be used in Matroska files.
</p>
<ul class="mini-toc">
<li><a href="#Options-48" accesskey="1">Options</a></li>
</ul>
<div class="subsection-level-extent" id="Options-48">
<h4 class="subsection"><span>10.1.1 Options<a class="copiable-link" href="#Options-48"> &para;</a></span></h4>

<dl class="table">
<dt><samp class="option">palette</samp></dt>
<dd><p>Specify the global palette used by the bitmaps.
</p>
<p>The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example <code class="code">0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b</code>.
</p>
</dd>
<dt><samp class="option">even_rows_fix</samp></dt>
<dd><p>When set to 1, enable a work-around that makes the number of pixel rows
even in all subtitles.  This fixes a problem with some players that
cut off the bottom row if the number is odd.  The work-around just adds
a fully transparent row if needed.  The overhead is low, typically
one byte per subtitle on average.
</p>
<p>By default, this work-around is disabled.
</p></dd>
</dl>


</div>
</div>
</div>
<div class="chapter-level-extent" id="See-Also">
<h2 class="chapter"><span>11 See Also<a class="copiable-link" href="#See-Also"> &para;</a></span></h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="libavcodec.html">libavcodec</a>
</p>

</div>
<div class="chapter-level-extent" id="Authors">
<h2 class="chapter"><span>12 Authors<a class="copiable-link" href="#Authors"> &para;</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

</div>
</div>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
