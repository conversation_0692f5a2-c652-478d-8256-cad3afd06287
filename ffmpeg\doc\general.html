<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      General Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      General Documentation
      </h1>


<div class="top-level-extent" id="SEC_Top">

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-External-libraries" href="#External-libraries">1 External libraries</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Alliance-for-Open-Media-_0028AOM_0029" href="#Alliance-for-Open-Media-_0028AOM_0029">1.1 Alliance for Open Media (AOM)</a></li>
    <li><a id="toc-AMD-AMF_002fVCE" href="#AMD-AMF_002fVCE">1.2 AMD AMF/VCE</a></li>
    <li><a id="toc-AviSynth" href="#AviSynth">1.3 AviSynth</a></li>
    <li><a id="toc-Chromaprint" href="#Chromaprint">1.4 Chromaprint</a></li>
    <li><a id="toc-codec2" href="#codec2">1.5 codec2</a></li>
    <li><a id="toc-dav1d" href="#dav1d">1.6 dav1d</a></li>
    <li><a id="toc-davs2" href="#davs2">1.7 davs2</a></li>
    <li><a id="toc-uavs3d" href="#uavs3d">1.8 uavs3d</a></li>
    <li><a id="toc-Game-Music-Emu" href="#Game-Music-Emu">1.9 Game Music Emu</a></li>
    <li><a id="toc-Intel-QuickSync-Video" href="#Intel-QuickSync-Video">1.10 Intel QuickSync Video</a></li>
    <li><a id="toc-Kvazaar" href="#Kvazaar">1.11 Kvazaar</a></li>
    <li><a id="toc-LAME" href="#LAME">1.12 LAME</a></li>
    <li><a id="toc-LCEVCdec" href="#LCEVCdec">1.13 LCEVCdec</a></li>
    <li><a id="toc-libilbc" href="#libilbc">1.14 libilbc</a></li>
    <li><a id="toc-libjxl" href="#libjxl">1.15 libjxl</a></li>
    <li><a id="toc-libvpx" href="#libvpx">1.16 libvpx</a></li>
    <li><a id="toc-ModPlug" href="#ModPlug">1.17 ModPlug</a></li>
    <li><a id="toc-OpenCORE_002c-VisualOn_002c-and-Fraunhofer-libraries" href="#OpenCORE_002c-VisualOn_002c-and-Fraunhofer-libraries">1.18 OpenCORE, VisualOn, and Fraunhofer libraries</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-OpenCORE-AMR" href="#OpenCORE-AMR">1.18.1 OpenCORE AMR</a></li>
      <li><a id="toc-VisualOn-AMR_002dWB-encoder-library" href="#VisualOn-AMR_002dWB-encoder-library">1.18.2 VisualOn AMR-WB encoder library</a></li>
      <li><a id="toc-Fraunhofer-AAC-library" href="#Fraunhofer-AAC-library">1.18.3 Fraunhofer AAC library</a></li>
      <li><a id="toc-LC3-library" href="#LC3-library">1.18.4 LC3 library</a></li>
    </ul></li>
    <li><a id="toc-OpenH264" href="#OpenH264">1.19 OpenH264</a></li>
    <li><a id="toc-OpenJPEG" href="#OpenJPEG">1.20 OpenJPEG</a></li>
    <li><a id="toc-rav1e" href="#rav1e">1.21 rav1e</a></li>
    <li><a id="toc-SVT_002dAV1" href="#SVT_002dAV1">1.22 SVT-AV1</a></li>
    <li><a id="toc-TwoLAME" href="#TwoLAME">1.23 TwoLAME</a></li>
    <li><a id="toc-VapourSynth" href="#VapourSynth">1.24 VapourSynth</a></li>
    <li><a id="toc-x264" href="#x264">1.25 x264</a></li>
    <li><a id="toc-x265" href="#x265">1.26 x265</a></li>
    <li><a id="toc-xavs" href="#xavs">1.27 xavs</a></li>
    <li><a id="toc-xavs2" href="#xavs2">1.28 xavs2</a></li>
    <li><a id="toc-eXtra_002dfast-Essential-Video-Encoder-_0028XEVE_0029" href="#eXtra_002dfast-Essential-Video-Encoder-_0028XEVE_0029">1.29 eXtra-fast Essential Video Encoder (XEVE)</a></li>
    <li><a id="toc-eXtra_002dfast-Essential-Video-Decoder-_0028XEVD_0029" href="#eXtra_002dfast-Essential-Video-Decoder-_0028XEVD_0029">1.30 eXtra-fast Essential Video Decoder (XEVD)</a></li>
    <li><a id="toc-ZVBI" href="#ZVBI">1.31 ZVBI</a></li>
  </ul></li>
  <li><a id="toc-Supported-File-Formats_002c-Codecs-or-Features" href="#Supported-File-Formats_002c-Codecs-or-Features">2 Supported File Formats, Codecs or Features</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-File-Formats" href="#File-Formats">2.1 File Formats</a></li>
    <li><a id="toc-Image-Formats" href="#Image-Formats">2.2 Image Formats</a></li>
    <li><a id="toc-Video-Codecs" href="#Video-Codecs">2.3 Video Codecs</a></li>
    <li><a id="toc-Audio-Codecs" href="#Audio-Codecs">2.4 Audio Codecs</a></li>
    <li><a id="toc-Subtitle-Formats" href="#Subtitle-Formats">2.5 Subtitle Formats</a></li>
    <li><a id="toc-Network-Protocols" href="#Network-Protocols">2.6 Network Protocols</a></li>
    <li><a id="toc-Input_002fOutput-Devices" href="#Input_002fOutput-Devices">2.7 Input/Output Devices</a></li>
    <li><a id="toc-Timecode" href="#Timecode">2.8 Timecode</a></li>
  </ul></li>
</ul>
</div>
</div>

<ul class="mini-toc">
<li><a href="#External-libraries" accesskey="1">External libraries</a></li>
<li><a href="#Supported-File-Formats_002c-Codecs-or-Features" accesskey="2">Supported File Formats, Codecs or Features</a></li>
</ul>
<div class="chapter-level-extent" id="External-libraries">
<h2 class="chapter"><span>1 External libraries<a class="copiable-link" href="#External-libraries"> &para;</a></span></h2>

<p>FFmpeg can be hooked up with a number of external libraries to add support
for more formats. None of them are used by default, their use has to be
explicitly requested by passing the appropriate flags to
<code class="command">./configure</code>.
</p>
<ul class="mini-toc">
<li><a href="#Alliance-for-Open-Media-_0028AOM_0029" accesskey="1">Alliance for Open Media (AOM)</a></li>
<li><a href="#AMD-AMF_002fVCE" accesskey="2">AMD AMF/VCE</a></li>
<li><a href="#AviSynth" accesskey="3">AviSynth</a></li>
<li><a href="#Chromaprint" accesskey="4">Chromaprint</a></li>
<li><a href="#codec2" accesskey="5">codec2</a></li>
<li><a href="#dav1d" accesskey="6">dav1d</a></li>
<li><a href="#davs2" accesskey="7">davs2</a></li>
<li><a href="#uavs3d" accesskey="8">uavs3d</a></li>
<li><a href="#Game-Music-Emu" accesskey="9">Game Music Emu</a></li>
<li><a href="#Intel-QuickSync-Video">Intel QuickSync Video</a></li>
<li><a href="#Kvazaar">Kvazaar</a></li>
<li><a href="#LAME">LAME</a></li>
<li><a href="#LCEVCdec">LCEVCdec</a></li>
<li><a href="#libilbc">libilbc</a></li>
<li><a href="#libjxl">libjxl</a></li>
<li><a href="#libvpx">libvpx</a></li>
<li><a href="#ModPlug">ModPlug</a></li>
<li><a href="#OpenCORE_002c-VisualOn_002c-and-Fraunhofer-libraries">OpenCORE, VisualOn, and Fraunhofer libraries</a></li>
<li><a href="#OpenH264">OpenH264</a></li>
<li><a href="#OpenJPEG">OpenJPEG</a></li>
<li><a href="#rav1e">rav1e</a></li>
<li><a href="#SVT_002dAV1">SVT-AV1</a></li>
<li><a href="#TwoLAME">TwoLAME</a></li>
<li><a href="#VapourSynth">VapourSynth</a></li>
<li><a href="#x264">x264</a></li>
<li><a href="#x265">x265</a></li>
<li><a href="#xavs">xavs</a></li>
<li><a href="#xavs2">xavs2</a></li>
<li><a href="#eXtra_002dfast-Essential-Video-Encoder-_0028XEVE_0029">eXtra-fast Essential Video Encoder (XEVE)</a></li>
<li><a href="#eXtra_002dfast-Essential-Video-Decoder-_0028XEVD_0029">eXtra-fast Essential Video Decoder (XEVD)</a></li>
<li><a href="#ZVBI">ZVBI</a></li>
</ul>
<div class="section-level-extent" id="Alliance-for-Open-Media-_0028AOM_0029">
<h3 class="section"><span>1.1 Alliance for Open Media (AOM)<a class="copiable-link" href="#Alliance-for-Open-Media-_0028AOM_0029"> &para;</a></span></h3>

<p>FFmpeg can make use of the AOM library for AV1 decoding and encoding.
</p>
<p>Go to <a class="url" href="http://aomedia.org/">http://aomedia.org/</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libaom</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="AMD-AMF_002fVCE">
<h3 class="section"><span>1.2 AMD AMF/VCE<a class="copiable-link" href="#AMD-AMF_002fVCE"> &para;</a></span></h3>

<p>FFmpeg can use the AMD Advanced Media Framework library
for accelerated H.264 and HEVC(only windows) encoding on hardware with Video Coding Engine (VCE).
</p>
<p>To enable support you must obtain the AMF framework header files(version 1.4.9+) from
<a class="url" href="https://github.com/GPUOpen-LibrariesAndSDKs/AMF.git">https://github.com/GPUOpen-LibrariesAndSDKs/AMF.git</a>.
</p>
<p>Create an <code class="code">AMF/</code> directory in the system include path.
Copy the contents of <code class="code">AMF/amf/public/include/</code> into that directory.
Then configure FFmpeg with <code class="code">--enable-amf</code>.
</p>
<p>Initialization of amf encoder occurs in this order:
1) trying to initialize through dx11(only windows)
2) trying to initialize through dx9(only windows)
3) trying to initialize through vulkan
</p>
<p>To use h.264(AMD VCE) encoder on linux amdgru-pro version 19.20+ and amf-amdgpu-pro
package(amdgru-pro contains, but does not install automatically) are required.
</p>
<p>This driver can be installed using amdgpu-pro-install script in official amd driver archive.
</p>
</div>
<div class="section-level-extent" id="AviSynth">
<h3 class="section"><span>1.3 AviSynth<a class="copiable-link" href="#AviSynth"> &para;</a></span></h3>

<p>FFmpeg can read AviSynth scripts as input. To enable support, pass
<code class="code">--enable-avisynth</code> to configure after installing the headers
provided by <a class="url" href="https://github.com/AviSynth/AviSynthPlus">AviSynth+</a>.
AviSynth+ can be configured to install only the headers by either
passing <code class="code">-DHEADERS_ONLY:bool=on</code> to the normal CMake-based build
system, or by using the supplied <code class="code">GNUmakefile</code>.
</p>
<p>For Windows, supported AviSynth variants are
<a class="url" href="http://avisynth.nl">AviSynth 2.6 RC1 or higher</a> for 32-bit builds and
<a class="url" href="http://avisynth.nl/index.php/AviSynth+">AviSynth+ r1718 or higher</a> for 32-bit and 64-bit builds.
</p>
<p>For Linux, macOS, and BSD, the only supported AviSynth variant is
<a class="url" href="https://github.com/AviSynth/AviSynthPlus">AviSynth+</a>, starting with version 3.5.
</p>
<div class="info">
<p>In 2016, AviSynth+ added support for building with GCC. However, due to
the eccentricities of Windows&rsquo; calling conventions, 32-bit GCC builds
of AviSynth+ are not compatible with typical 32-bit builds of FFmpeg.
</p>
<p>By default, FFmpeg assumes compatibility with 32-bit MSVC builds of
AviSynth+ since that is the most widely-used and entrenched build
configuration.  Users can override this and enable support for 32-bit
GCC builds of AviSynth+ by passing <code class="code">-DAVSC_WIN32_GCC32</code> to
<code class="code">--extra-cflags</code> when configuring FFmpeg.
</p>
<p>64-bit builds of FFmpeg are not affected, and can use either MSVC or
GCC builds of AviSynth+ without any special flags.
</p></div>
<div class="info">
<p>AviSynth(+) is loaded dynamically.  Distributors can build FFmpeg
with <code class="code">--enable-avisynth</code>, and the binaries will work regardless
of the end user having AviSynth installed.  If/when an end user
would like to use AviSynth scripts, then they can install AviSynth(+)
and FFmpeg will be able to find and use it to open scripts.
</p></div>
</div>
<div class="section-level-extent" id="Chromaprint">
<h3 class="section"><span>1.4 Chromaprint<a class="copiable-link" href="#Chromaprint"> &para;</a></span></h3>

<p>FFmpeg can make use of the Chromaprint library for generating audio fingerprints.
Pass <code class="code">--enable-chromaprint</code> to configure to
enable it. See <a class="url" href="https://acoustid.org/chromaprint">https://acoustid.org/chromaprint</a>.
</p>
</div>
<div class="section-level-extent" id="codec2">
<h3 class="section"><span>1.5 codec2<a class="copiable-link" href="#codec2"> &para;</a></span></h3>

<p>FFmpeg can make use of the codec2 library for codec2 decoding and encoding.
There is currently no native decoder, so libcodec2 must be used for decoding.
</p>
<p>Go to <a class="url" href="http://freedv.org/">http://freedv.org/</a>, download &quot;Codec 2 source archive&quot;.
Build and install using CMake. Debian users can install the libcodec2-dev package instead.
Once libcodec2 is installed you can pass <code class="code">--enable-libcodec2</code> to configure to enable it.
</p>
<p>The easiest way to use codec2 is with .c2 files, since they contain the mode information required for decoding.
To encode such a file, use a .c2 file extension and give the libcodec2 encoder the -mode option:
<code class="code">ffmpeg -i input.wav -mode 700C output.c2</code>.
Playback is as simple as <code class="code">ffplay output.c2</code>.
For a list of supported modes, run <code class="code">ffmpeg -h encoder=libcodec2</code>.
Raw codec2 files are also supported.
To make sense of them the mode in use needs to be specified as a format option:
<code class="code">ffmpeg -f codec2raw -mode 1300 -i input.raw output.wav</code>.
</p>
</div>
<div class="section-level-extent" id="dav1d">
<h3 class="section"><span>1.6 dav1d<a class="copiable-link" href="#dav1d"> &para;</a></span></h3>

<p>FFmpeg can make use of the dav1d library for AV1 video decoding.
</p>
<p>Go to <a class="url" href="https://code.videolan.org/videolan/dav1d">https://code.videolan.org/videolan/dav1d</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libdav1d</code> to configure to enable it.
</p>
</div>
<div class="section-level-extent" id="davs2">
<h3 class="section"><span>1.7 davs2<a class="copiable-link" href="#davs2"> &para;</a></span></h3>

<p>FFmpeg can make use of the davs2 library for AVS2-P2/IEEE1857.4 video decoding.
</p>
<p>Go to <a class="url" href="https://github.com/pkuvcl/davs2">https://github.com/pkuvcl/davs2</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libdavs2</code> to configure to
enable it.
</p>
<div class="info">
<p>libdavs2 is under the GNU Public License Version 2 or later
(see <a class="url" href="http://www.gnu.org/licenses/old-licenses/gpl-2.0.html">http://www.gnu.org/licenses/old-licenses/gpl-2.0.html</a> for
details), you must upgrade FFmpeg&rsquo;s license to GPL in order to use it.
</p></div>
</div>
<div class="section-level-extent" id="uavs3d">
<h3 class="section"><span>1.8 uavs3d<a class="copiable-link" href="#uavs3d"> &para;</a></span></h3>

<p>FFmpeg can make use of the uavs3d library for AVS3-P2/IEEE1857.10 video decoding.
</p>
<p>Go to <a class="url" href="https://github.com/uavs3/uavs3d">https://github.com/uavs3/uavs3d</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libuavs3d</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="Game-Music-Emu">
<h3 class="section"><span>1.9 Game Music Emu<a class="copiable-link" href="#Game-Music-Emu"> &para;</a></span></h3>

<p>FFmpeg can make use of the Game Music Emu library to read audio from supported video game
music file formats. Pass <code class="code">--enable-libgme</code> to configure to
enable it. See <a class="url" href="https://bitbucket.org/mpyne/game-music-emu/overview">https://bitbucket.org/mpyne/game-music-emu/overview</a>.
</p>
</div>
<div class="section-level-extent" id="Intel-QuickSync-Video">
<h3 class="section"><span>1.10 Intel QuickSync Video<a class="copiable-link" href="#Intel-QuickSync-Video"> &para;</a></span></h3>

<p>FFmpeg can use Intel QuickSync Video (QSV) for accelerated decoding and encoding
of multiple codecs. To use QSV, FFmpeg must be linked against the <code class="code">libmfx</code>
dispatcher, which loads the actual decoding libraries.
</p>
<p>The dispatcher is open source and can be downloaded from
<a class="url" href="https://github.com/lu-zero/mfx_dispatch.git">https://github.com/lu-zero/mfx_dispatch.git</a>. FFmpeg needs to be configured
with the <code class="code">--enable-libmfx</code> option and <code class="code">pkg-config</code> needs to be able to
locate the dispatcher&rsquo;s <code class="code">.pc</code> files.
</p>
</div>
<div class="section-level-extent" id="Kvazaar">
<h3 class="section"><span>1.11 Kvazaar<a class="copiable-link" href="#Kvazaar"> &para;</a></span></h3>

<p>FFmpeg can make use of the Kvazaar library for HEVC encoding.
</p>
<p>Go to <a class="url" href="https://github.com/ultravideo/kvazaar">https://github.com/ultravideo/kvazaar</a> and follow the
instructions for installing the library. Then pass
<code class="code">--enable-libkvazaar</code> to configure to enable it.
</p>
</div>
<div class="section-level-extent" id="LAME">
<h3 class="section"><span>1.12 LAME<a class="copiable-link" href="#LAME"> &para;</a></span></h3>

<p>FFmpeg can make use of the LAME library for MP3 encoding.
</p>
<p>Go to <a class="url" href="http://lame.sourceforge.net/">http://lame.sourceforge.net/</a> and follow the
instructions for installing the library.
Then pass <code class="code">--enable-libmp3lame</code> to configure to enable it.
</p>
</div>
<div class="section-level-extent" id="LCEVCdec">
<h3 class="section"><span>1.13 LCEVCdec<a class="copiable-link" href="#LCEVCdec"> &para;</a></span></h3>

<p>FFmpeg can make use of the liblcevc_dec library for LCEVC enhacement layer
decoding on supported bitstreams.
</p>
<p>Go to <a class="url" href="https://github.com/v-novaltd/LCEVCdec">https://github.com/v-novaltd/LCEVCdec</a> and follow the instructions
for installing the library. Then pass <code class="code">--enable-liblcevc-dec</code> to configure to
enable it.
</p>
<div class="info">
<p>LCEVCdec is under the BSD-3-Clause-Clear License.
</p></div>
</div>
<div class="section-level-extent" id="libilbc">
<h3 class="section"><span>1.14 libilbc<a class="copiable-link" href="#libilbc"> &para;</a></span></h3>

<p>iLBC is a narrowband speech codec that has been made freely available
by Google as part of the WebRTC project. libilbc is a packaging friendly
copy of the iLBC codec. FFmpeg can make use of the libilbc library for
iLBC decoding and encoding.
</p>
<p>Go to <a class="url" href="https://github.com/TimothyGu/libilbc">https://github.com/TimothyGu/libilbc</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libilbc</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="libjxl">
<h3 class="section"><span>1.15 libjxl<a class="copiable-link" href="#libjxl"> &para;</a></span></h3>

<p>JPEG XL is an image format intended to fully replace legacy JPEG for an extended
period of life. See <a class="url" href="https://jpegxl.info/">https://jpegxl.info/</a> for more information, and see
<a class="url" href="https://github.com/libjxl/libjxl">https://github.com/libjxl/libjxl</a> for the library source. You can pass
<code class="code">--enable-libjxl</code> to configure in order enable the libjxl wrapper.
</p>
</div>
<div class="section-level-extent" id="libvpx">
<h3 class="section"><span>1.16 libvpx<a class="copiable-link" href="#libvpx"> &para;</a></span></h3>

<p>FFmpeg can make use of the libvpx library for VP8/VP9 decoding and encoding.
</p>
<p>Go to <a class="url" href="http://www.webmproject.org/">http://www.webmproject.org/</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libvpx</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="ModPlug">
<h3 class="section"><span>1.17 ModPlug<a class="copiable-link" href="#ModPlug"> &para;</a></span></h3>

<p>FFmpeg can make use of this library, originating in Modplug-XMMS, to read from MOD-like music files.
See <a class="url" href="https://github.com/Konstanty/libmodplug">https://github.com/Konstanty/libmodplug</a>. Pass <code class="code">--enable-libmodplug</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="OpenCORE_002c-VisualOn_002c-and-Fraunhofer-libraries">
<h3 class="section"><span>1.18 OpenCORE, VisualOn, and Fraunhofer libraries<a class="copiable-link" href="#OpenCORE_002c-VisualOn_002c-and-Fraunhofer-libraries"> &para;</a></span></h3>

<p>Spun off Google Android sources, OpenCore, VisualOn and Fraunhofer
libraries provide encoders for a number of audio codecs.
</p>
<div class="info">
<p>OpenCORE and VisualOn libraries are under the Apache License 2.0
(see <a class="url" href="http://www.apache.org/licenses/LICENSE-2.0">http://www.apache.org/licenses/LICENSE-2.0</a> for details), which is
incompatible to the LGPL version 2.1 and GPL version 2. You have to
upgrade FFmpeg&rsquo;s license to LGPL version 3 (or if you have enabled
GPL components, GPL version 3) by passing <code class="code">--enable-version3</code> to configure in
order to use it.
</p>
<p>The license of the Fraunhofer AAC library is incompatible with the GPL.
Therefore, for GPL builds, you have to pass <code class="code">--enable-nonfree</code> to
configure in order to use it. To the best of our knowledge, it is
compatible with the LGPL.
</p></div>
<ul class="mini-toc">
<li><a href="#OpenCORE-AMR" accesskey="1">OpenCORE AMR</a></li>
<li><a href="#VisualOn-AMR_002dWB-encoder-library" accesskey="2">VisualOn AMR-WB encoder library</a></li>
<li><a href="#Fraunhofer-AAC-library" accesskey="3">Fraunhofer AAC library</a></li>
<li><a href="#LC3-library" accesskey="4">LC3 library</a></li>
</ul>
<div class="subsection-level-extent" id="OpenCORE-AMR">
<h4 class="subsection"><span>1.18.1 OpenCORE AMR<a class="copiable-link" href="#OpenCORE-AMR"> &para;</a></span></h4>

<p>FFmpeg can make use of the OpenCORE libraries for AMR-NB
decoding/encoding and AMR-WB decoding.
</p>
<p>Go to <a class="url" href="http://sourceforge.net/projects/opencore-amr/">http://sourceforge.net/projects/opencore-amr/</a> and follow the
instructions for installing the libraries.
Then pass <code class="code">--enable-libopencore-amrnb</code> and/or
<code class="code">--enable-libopencore-amrwb</code> to configure to enable them.
</p>
</div>
<div class="subsection-level-extent" id="VisualOn-AMR_002dWB-encoder-library">
<h4 class="subsection"><span>1.18.2 VisualOn AMR-WB encoder library<a class="copiable-link" href="#VisualOn-AMR_002dWB-encoder-library"> &para;</a></span></h4>

<p>FFmpeg can make use of the VisualOn AMR-WBenc library for AMR-WB encoding.
</p>
<p>Go to <a class="url" href="http://sourceforge.net/projects/opencore-amr/">http://sourceforge.net/projects/opencore-amr/</a> and follow the
instructions for installing the library.
Then pass <code class="code">--enable-libvo-amrwbenc</code> to configure to enable it.
</p>
</div>
<div class="subsection-level-extent" id="Fraunhofer-AAC-library">
<h4 class="subsection"><span>1.18.3 Fraunhofer AAC library<a class="copiable-link" href="#Fraunhofer-AAC-library"> &para;</a></span></h4>

<p>FFmpeg can make use of the Fraunhofer AAC library for AAC decoding &amp; encoding.
</p>
<p>Go to <a class="url" href="http://sourceforge.net/projects/opencore-amr/">http://sourceforge.net/projects/opencore-amr/</a> and follow the
instructions for installing the library.
Then pass <code class="code">--enable-libfdk-aac</code> to configure to enable it.
</p>
</div>
<div class="subsection-level-extent" id="LC3-library">
<h4 class="subsection"><span>1.18.4 LC3 library<a class="copiable-link" href="#LC3-library"> &para;</a></span></h4>

<p>FFmpeg can make use of the Google LC3 library for LC3 decoding &amp; encoding.
</p>
<p>Go to <a class="url" href="https://github.com/google/liblc3/">https://github.com/google/liblc3/</a> and follow the instructions for
installing the library.
Then pass <code class="code">--enable-liblc3</code> to configure to enable it.
</p>
</div>
</div>
<div class="section-level-extent" id="OpenH264">
<h3 class="section"><span>1.19 OpenH264<a class="copiable-link" href="#OpenH264"> &para;</a></span></h3>

<p>FFmpeg can make use of the OpenH264 library for H.264 decoding and encoding.
</p>
<p>Go to <a class="url" href="http://www.openh264.org/">http://www.openh264.org/</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libopenh264</code> to configure to
enable it.
</p>
<p>For decoding, this library is much more limited than the built-in decoder
in libavcodec; currently, this library lacks support for decoding B-frames
and some other main/high profile features. (It currently only supports
constrained baseline profile and CABAC.) Using it is mostly useful for
testing and for taking advantage of Cisco&rsquo;s patent portfolio license
(<a class="url" href="http://www.openh264.org/BINARY_LICENSE.txt">http://www.openh264.org/BINARY_LICENSE.txt</a>).
</p>
</div>
<div class="section-level-extent" id="OpenJPEG">
<h3 class="section"><span>1.20 OpenJPEG<a class="copiable-link" href="#OpenJPEG"> &para;</a></span></h3>

<p>FFmpeg can use the OpenJPEG libraries for decoding/encoding J2K videos.  Go to
<a class="url" href="http://www.openjpeg.org/">http://www.openjpeg.org/</a> to get the libraries and follow the installation
instructions.  To enable using OpenJPEG in FFmpeg, pass <code class="code">--enable-libopenjpeg</code> to
<samp class="file">./configure</samp>.
</p>
</div>
<div class="section-level-extent" id="rav1e">
<h3 class="section"><span>1.21 rav1e<a class="copiable-link" href="#rav1e"> &para;</a></span></h3>

<p>FFmpeg can make use of rav1e (Rust AV1 Encoder) via its C bindings to encode videos.
Go to <a class="url" href="https://github.com/xiph/rav1e/">https://github.com/xiph/rav1e/</a> and follow the instructions to build
the C library. To enable using rav1e in FFmpeg, pass <code class="code">--enable-librav1e</code>
to <samp class="file">./configure</samp>.
</p>
</div>
<div class="section-level-extent" id="SVT_002dAV1">
<h3 class="section"><span>1.22 SVT-AV1<a class="copiable-link" href="#SVT_002dAV1"> &para;</a></span></h3>

<p>FFmpeg can make use of the Scalable Video Technology for AV1 library for AV1 encoding.
</p>
<p>Go to <a class="url" href="https://gitlab.com/AOMediaCodec/SVT-AV1/">https://gitlab.com/AOMediaCodec/SVT-AV1/</a> and follow the instructions
for installing the library. Then pass <code class="code">--enable-libsvtav1</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="TwoLAME">
<h3 class="section"><span>1.23 TwoLAME<a class="copiable-link" href="#TwoLAME"> &para;</a></span></h3>

<p>FFmpeg can make use of the TwoLAME library for MP2 encoding.
</p>
<p>Go to <a class="url" href="http://www.twolame.org/">http://www.twolame.org/</a> and follow the
instructions for installing the library.
Then pass <code class="code">--enable-libtwolame</code> to configure to enable it.
</p>
</div>
<div class="section-level-extent" id="VapourSynth">
<h3 class="section"><span>1.24 VapourSynth<a class="copiable-link" href="#VapourSynth"> &para;</a></span></h3>

<p>FFmpeg can read VapourSynth scripts as input. To enable support, pass
<code class="code">--enable-vapoursynth</code> to configure. Vapoursynth is detected via
<code class="code">pkg-config</code>. Versions 42 or greater supported.
See <a class="url" href="http://www.vapoursynth.com/">http://www.vapoursynth.com/</a>.
</p>
<p>Due to security concerns, Vapoursynth scripts will not
be autodetected so the input format has to be forced. For ff* CLI tools,
add <code class="code">-f vapoursynth</code> before the input <code class="code">-i yourscript.vpy</code>.
</p>
</div>
<div class="section-level-extent" id="x264">
<h3 class="section"><span>1.25 x264<a class="copiable-link" href="#x264"> &para;</a></span></h3>

<p>FFmpeg can make use of the x264 library for H.264 encoding.
</p>
<p>Go to <a class="url" href="http://www.videolan.org/developers/x264.html">http://www.videolan.org/developers/x264.html</a> and follow the
instructions for installing the library. Then pass <code class="code">--enable-libx264</code> to
configure to enable it.
</p>
<div class="info">
<p>x264 is under the GNU Public License Version 2 or later
(see <a class="url" href="http://www.gnu.org/licenses/old-licenses/gpl-2.0.html">http://www.gnu.org/licenses/old-licenses/gpl-2.0.html</a> for
details), you must upgrade FFmpeg&rsquo;s license to GPL in order to use it.
</p></div>
</div>
<div class="section-level-extent" id="x265">
<h3 class="section"><span>1.26 x265<a class="copiable-link" href="#x265"> &para;</a></span></h3>

<p>FFmpeg can make use of the x265 library for HEVC encoding.
</p>
<p>Go to <a class="url" href="http://x265.org/developers.html">http://x265.org/developers.html</a> and follow the instructions
for installing the library. Then pass <code class="code">--enable-libx265</code> to configure
to enable it.
</p>
<div class="info">
<p>x265 is under the GNU Public License Version 2 or later
(see <a class="url" href="http://www.gnu.org/licenses/old-licenses/gpl-2.0.html">http://www.gnu.org/licenses/old-licenses/gpl-2.0.html</a> for
details), you must upgrade FFmpeg&rsquo;s license to GPL in order to use it.
</p></div>
</div>
<div class="section-level-extent" id="xavs">
<h3 class="section"><span>1.27 xavs<a class="copiable-link" href="#xavs"> &para;</a></span></h3>

<p>FFmpeg can make use of the xavs library for AVS encoding.
</p>
<p>Go to <a class="url" href="http://xavs.sf.net/">http://xavs.sf.net/</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libxavs</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="xavs2">
<h3 class="section"><span>1.28 xavs2<a class="copiable-link" href="#xavs2"> &para;</a></span></h3>

<p>FFmpeg can make use of the xavs2 library for AVS2-P2/IEEE1857.4 video encoding.
</p>
<p>Go to <a class="url" href="https://github.com/pkuvcl/xavs2">https://github.com/pkuvcl/xavs2</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libxavs2</code> to configure to
enable it.
</p>
<div class="info">
<p>libxavs2 is under the GNU Public License Version 2 or later
(see <a class="url" href="http://www.gnu.org/licenses/old-licenses/gpl-2.0.html">http://www.gnu.org/licenses/old-licenses/gpl-2.0.html</a> for
details), you must upgrade FFmpeg&rsquo;s license to GPL in order to use it.
</p></div>
</div>
<div class="section-level-extent" id="eXtra_002dfast-Essential-Video-Encoder-_0028XEVE_0029">
<h3 class="section"><span>1.29 eXtra-fast Essential Video Encoder (XEVE)<a class="copiable-link" href="#eXtra_002dfast-Essential-Video-Encoder-_0028XEVE_0029"> &para;</a></span></h3>

<p>FFmpeg can make use of the XEVE library for EVC video encoding.
</p>
<p>Go to <a class="url" href="https://github.com/mpeg5/xeve">https://github.com/mpeg5/xeve</a> and follow the instructions for
installing the XEVE library. Then pass <code class="code">--enable-libxeve</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="eXtra_002dfast-Essential-Video-Decoder-_0028XEVD_0029">
<h3 class="section"><span>1.30 eXtra-fast Essential Video Decoder (XEVD)<a class="copiable-link" href="#eXtra_002dfast-Essential-Video-Decoder-_0028XEVD_0029"> &para;</a></span></h3>

<p>FFmpeg can make use of the XEVD library for EVC video decoding.
</p>
<p>Go to <a class="url" href="https://github.com/mpeg5/xevd">https://github.com/mpeg5/xevd</a> and follow the instructions for
installing the XEVD library. Then pass <code class="code">--enable-libxevd</code> to configure to
enable it.
</p>
</div>
<div class="section-level-extent" id="ZVBI">
<h3 class="section"><span>1.31 ZVBI<a class="copiable-link" href="#ZVBI"> &para;</a></span></h3>

<p>ZVBI is a VBI decoding library which can be used by FFmpeg to decode DVB
teletext pages and DVB teletext subtitles.
</p>
<p>Go to <a class="url" href="http://sourceforge.net/projects/zapping/">http://sourceforge.net/projects/zapping/</a> and follow the instructions for
installing the library. Then pass <code class="code">--enable-libzvbi</code> to configure to
enable it.
</p>
</div>
</div>
<div class="chapter-level-extent" id="Supported-File-Formats_002c-Codecs-or-Features">
<h2 class="chapter"><span>2 Supported File Formats, Codecs or Features<a class="copiable-link" href="#Supported-File-Formats_002c-Codecs-or-Features"> &para;</a></span></h2>

<p>You can use the <code class="code">-formats</code> and <code class="code">-codecs</code> options to have an exhaustive list.
</p>
<ul class="mini-toc">
<li><a href="#File-Formats" accesskey="1">File Formats</a></li>
<li><a href="#Image-Formats" accesskey="2">Image Formats</a></li>
<li><a href="#Video-Codecs" accesskey="3">Video Codecs</a></li>
<li><a href="#Audio-Codecs" accesskey="4">Audio Codecs</a></li>
<li><a href="#Subtitle-Formats" accesskey="5">Subtitle Formats</a></li>
<li><a href="#Network-Protocols" accesskey="6">Network Protocols</a></li>
<li><a href="#Input_002fOutput-Devices" accesskey="7">Input/Output Devices</a></li>
<li><a href="#Timecode" accesskey="8">Timecode</a></li>
</ul>
<div class="section-level-extent" id="File-Formats">
<h3 class="section"><span>2.1 File Formats<a class="copiable-link" href="#File-Formats"> &para;</a></span></h3>

<p>FFmpeg supports the following file formats through the <code class="code">libavformat</code>
library:
</p>
<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Encoding</td><td width="10%">Decoding</td><td width="40%">Comments</td></tr>
<tr><td width="40%">3dostr</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">4xm</td><td width="10%"></td><td width="10%">X</td><td width="40%">4X Technologies format, used in some games.</td></tr>
<tr><td width="40%">8088flex TMV</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">AAX</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audible Enhanced Audio format, used in audiobooks.</td></tr>
<tr><td width="40%">AA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audible Format 2, 3, and 4, used in audiobooks.</td></tr>
<tr><td width="40%">ACT Voice</td><td width="10%"></td><td width="10%">X</td><td width="40%">contains G.729 audio</td></tr>
<tr><td width="40%">Adobe Filmstrip</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Audio IFF (AIFF)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">American Laser Games MM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used in games like Mad Dog McCree.</td></tr>
<tr><td width="40%">3GPP AMR</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Amazing Studio Packed Animation File</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used in game Heart Of Darkness.</td></tr>
<tr><td width="40%">Apple HTTP Live Streaming</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Artworx Data Format</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Interplay ACM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio only format used in some Interplay games.</td></tr>
<tr><td width="40%">ADP</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the Nintendo Gamecube.</td></tr>
<tr><td width="40%">AFC</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the Nintendo Gamecube.</td></tr>
<tr><td width="40%">ADS/SS2</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the PS2.</td></tr>
<tr><td width="40%">APNG</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ASF</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Advanced / Active Streaming Format.</td></tr>
<tr><td width="40%">AST</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Audio format used on the Nintendo Wii.</td></tr>
<tr><td width="40%">AVI</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">AviSynth</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">AVR</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on Mac.</td></tr>
<tr><td width="40%">AVS</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used by the Creature Shock game.</td></tr>
<tr><td width="40%">Beam Software SIFF</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio and video format used in some games by Beam Software.</td></tr>
<tr><td width="40%">Bethesda Softworks VID</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some games from Bethesda Softworks.</td></tr>
<tr><td width="40%">Binary text</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Bink</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used by many games.</td></tr>
<tr><td width="40%">Bink Audio</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio only multimedia format used by some games.</td></tr>
<tr><td width="40%">Bitmap Brothers JV</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Z and Z95 games.</td></tr>
<tr><td width="40%">BRP</td><td width="10%"></td><td width="10%">X</td><td width="40%">Argonaut Games format.</td></tr>
<tr><td width="40%">Brute Force &amp; Ignorance</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in the game Flash Traffic: City of Angels.</td></tr>
<tr><td width="40%">BFSTM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the Nintendo WiiU (based on BRSTM).</td></tr>
<tr><td width="40%">BRSTM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the Nintendo Wii.</td></tr>
<tr><td width="40%">BW64</td><td width="10%"></td><td width="10%">X</td><td width="40%">Broadcast Wave 64bit.</td></tr>
<tr><td width="40%">BWF</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">codec2 (raw)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Must be given -mode format option to decode correctly.</td></tr>
<tr><td width="40%">codec2 (.c2 files)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Contains header with version and mode info, simplifying playback.</td></tr>
<tr><td width="40%">CRI ADX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Audio-only format used in console video games.</td></tr>
<tr><td width="40%">CRI AIX</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">CRI HCA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio-only format used in console video games.</td></tr>
<tr><td width="40%">Discworld II BMV</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Interplay C93</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in the game Cyberia from Interplay.</td></tr>
<tr><td width="40%">Delphine Software International CIN</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used by Delphine Software games.</td></tr>
<tr><td width="40%">Digital Speech Standard (DSS)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">CD+G</td><td width="10%"></td><td width="10%">X</td><td width="40%">Video format used by CD+G karaoke disks</td></tr>
<tr><td width="40%">Phantom Cine</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Commodore CDXL</td><td width="10%"></td><td width="10%">X</td><td width="40%">Amiga CD video format</td></tr>
<tr><td width="40%">Core Audio Format</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Apple Core Audio Format</td></tr>
<tr><td width="40%">CRC testing format</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">Creative Voice</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Created for the Sound Blaster Pro.</td></tr>
<tr><td width="40%">CRYO APC</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used in some games by CRYO Interactive Entertainment.</td></tr>
<tr><td width="40%">D-Cinema audio</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Deluxe Paint Animation</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DCSTR</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DFA</td><td width="10%"></td><td width="10%">X</td><td width="40%">This format is used in Chronomaster game</td></tr>
<tr><td width="40%">DirectDraw Surface</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSD Stream File (DSF)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DV video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">DXA</td><td width="10%"></td><td width="10%">X</td><td width="40%">This format is used in the non-Windows version of the Feeble Files
         game and different game cutscenes repacked for use with ScummVM.</td></tr>
<tr><td width="40%">Electronic Arts cdata</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Electronic Arts Multimedia</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in various EA games; files have extensions like WVE and UV2.</td></tr>
<tr><td width="40%">Ensoniq Paris Audio File</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">FFM (FFserver live feed)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Flash (SWF)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Flash 9 (AVM2)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Only embedded audio is decoded.</td></tr>
<tr><td width="40%">FLI/FLC/FLX animation</td><td width="10%"></td><td width="10%">X</td><td width="40%">.fli/.flc files</td></tr>
<tr><td width="40%">Flash Video (FLV)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Macromedia Flash video files</td></tr>
<tr><td width="40%">framecrc testing format</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">FunCom ISS</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used in various games from FunCom like The Longest Journey.</td></tr>
<tr><td width="40%">G.723.1</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">G.726</td><td width="10%"></td><td width="10%">X</td><td width="40%">Both left- and right-justified.</td></tr>
<tr><td width="40%">G.729 BIT</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">G.729 raw</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">GENH</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format for various games.</td></tr>
<tr><td width="40%">GIF Animation</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">GXF</td><td width="10%">X</td><td width="10%">X</td><td width="40%">General eXchange Format SMPTE 360M, used by Thomson Grass Valley
         playout servers.</td></tr>
<tr><td width="40%">HNM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Only version 4 supported, used in some games from Cryo Interactive</td></tr>
<tr><td width="40%">iCEDraw File</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ICO</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Microsoft Windows ICO</td></tr>
<tr><td width="40%">id Quake II CIN video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">id RoQ</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Quake III, Jedi Knight 2 and other computer games.</td></tr>
<tr><td width="40%">IEC61937 encapsulation</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">IFF</td><td width="10%"></td><td width="10%">X</td><td width="40%">Interchange File Format</td></tr>
<tr><td width="40%">IFV</td><td width="10%"></td><td width="10%">X</td><td width="40%">A format used by some old CCTV DVRs.</td></tr>
<tr><td width="40%">iLBC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Interplay MVE</td><td width="10%"></td><td width="10%">X</td><td width="40%">Format used in various Interplay computer games.</td></tr>
<tr><td width="40%">Iterated Systems ClearVideo</td><td width="10%"></td><td width="10%">X</td><td width="40%">I-frames only</td></tr>
<tr><td width="40%">IV8</td><td width="10%"></td><td width="10%">X</td><td width="40%">A format generated by IndigoVision 8000 video server.</td></tr>
<tr><td width="40%">IVF (On2)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">A format used by libvpx</td></tr>
<tr><td width="40%">Internet Video Recording</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">IRCAM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">LAF</td><td width="10%"></td><td width="10%">X</td><td width="40%">Limitless Audio Format</td></tr>
<tr><td width="40%">LATM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">LMLM4</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used by Linux Media Labs MPEG-4 PCI boards</td></tr>
<tr><td width="40%">LOAS</td><td width="10%"></td><td width="10%">X</td><td width="40%">contains LATM multiplexed AAC audio</td></tr>
<tr><td width="40%">LRC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">LVF</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LXF</td><td width="10%"></td><td width="10%">X</td><td width="40%">VR native stream format, used by Leitch/Harris&rsquo; video servers.</td></tr>
<tr><td width="40%">Magic Lantern Video (MLV)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Matroska</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Matroska audio</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">FFmpeg metadata</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Metadata in text format.</td></tr>
<tr><td width="40%">MAXIS XA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sim City 3000; file extension .xa.</td></tr>
<tr><td width="40%">MCA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some games from Capcom; file extension .mca.</td></tr>
<tr><td width="40%">MD Studio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Metal Gear Solid: The Twin Snakes</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Megalux Frame</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used by Megalux Ultimate Paint</td></tr>
<tr><td width="40%">MobiClip MODS</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MobiClip MOFLEX</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Mobotix .mxg</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Monkey&rsquo;s Audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Motion Pixels MVI</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MOV/QuickTime/MP4</td><td width="10%">X</td><td width="10%">X</td><td width="40%">3GP, 3GP2, PSP, iPod variants supported</td></tr>
<tr><td width="40%">MP2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MP3</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-1 System</td><td width="10%">X</td><td width="10%">X</td><td width="40%">muxed audio and video, VCD format supported</td></tr>
<tr><td width="40%">MPEG-PS (program stream)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">also known as <code class="code">VOB</code> file, SVCD and DVD format supported</td></tr>
<tr><td width="40%">MPEG-TS (transport stream)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">also known as DVB Transport Stream</td></tr>
<tr><td width="40%">MPEG-4</td><td width="10%">X</td><td width="10%">X</td><td width="40%">MPEG-4 is a variant of QuickTime.</td></tr>
<tr><td width="40%">MSF</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the PS3.</td></tr>
<tr><td width="40%">Mirillis FIC video</td><td width="10%"></td><td width="10%">X</td><td width="40%">No cursor rendering.</td></tr>
<tr><td width="40%">MIDI Sample Dump Standard</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MIME multipart JPEG</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">MSN TCP webcam</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used by MSN Messenger webcam streams.</td></tr>
<tr><td width="40%">MTV</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Musepack</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Musepack SV8</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Material eXchange Format (MXF)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">SMPTE 377M, used by D-Cinema, broadcast industry.</td></tr>
<tr><td width="40%">Material eXchange Format (MXF), D-10 Mapping</td><td width="10%">X</td><td width="10%">X</td><td width="40%">SMPTE 386M, D-10/IMX Mapping.</td></tr>
<tr><td width="40%">NC camera feed</td><td width="10%"></td><td width="10%">X</td><td width="40%">NC (AVIP NC4600) camera streams</td></tr>
<tr><td width="40%">NIST SPeech HEader REsources</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Computerized Speech Lab NSP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">NTT TwinVQ (VQF)</td><td width="10%"></td><td width="10%">X</td><td width="40%">Nippon Telegraph and Telephone Corporation TwinVQ.</td></tr>
<tr><td width="40%">Nullsoft Streaming Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">NuppelVideo</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">NUT</td><td width="10%">X</td><td width="10%">X</td><td width="40%">NUT Open Container Format</td></tr>
<tr><td width="40%">Ogg</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Playstation Portable PMP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Portable Voice Format</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RK Audio (RKA)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">TechnoTrend PVA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used by TechnoTrend DVB PCI boards.</td></tr>
<tr><td width="40%">QCP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw ADTS (AAC)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw AC-3</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw AMR-NB</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw AMR-WB</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw APAC</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw aptX</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw aptX HD</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw Bonk</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw Chinese AVS video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw DFPWM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw Dirac</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw DNxHD</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw DTS</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw DTS-HD</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw E-AC-3</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw EVC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw FLAC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw GSM</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw H.261</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw H.263</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw H.264</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw HEVC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw Ingenient MJPEG</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw MJPEG</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw MLP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw MPEG</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw MPEG-1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw MPEG-2</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw MPEG-4</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw NULL</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">raw video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw id RoQ</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">raw OBU</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw OSQ</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw SBC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw Shorten</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw TAK</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw TrueHD</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw VC-1</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM A-law</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM mu-law</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM Archimedes VIDC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 8 bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 16 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 16 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 24 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 24 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 32 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 32 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 64 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM signed 64 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 8 bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 16 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 16 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 24 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 24 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 32 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM unsigned 32 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM 16.8 floating point little-endian</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM 24.0 floating point little-endian</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM floating-point 32 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM floating-point 32 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM floating-point 64 bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">raw PCM floating-point 64 bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RDT</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">REDCODE R3D</td><td width="10%"></td><td width="10%">X</td><td width="40%">File format used by RED Digital cameras, contains JPEG 2000 frames and PCM audio.</td></tr>
<tr><td width="40%">RealMedia</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Redirector</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RedSpark</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Renderware TeXture Dictionary</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Resolume DXV</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Encoding is only supported for the DXT1 (Normal Quality, No Alpha) texture format.</td></tr>
<tr><td width="40%">RF64</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RL2</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio and video format used in some games by Entertainment Software Partners.</td></tr>
<tr><td width="40%">RPL/ARMovie</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Lego Mindstorms RSO</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RSD</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RTMP</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Output is performed by publishing stream to RTMP server</td></tr>
<tr><td width="40%">RTP</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RTSP</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Sample Dump eXchange</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SAP</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SBG</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SDNS</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SDP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SER</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Digital Pictures SGA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sega FILM/CPK</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in many Sega Saturn console games.</td></tr>
<tr><td width="40%">Silicon Graphics Movie</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sierra SOL</td><td width="10%"></td><td width="10%">X</td><td width="40%">.sol files used in Sierra Online games.</td></tr>
<tr><td width="40%">Sierra VMD</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sierra CD-ROM games.</td></tr>
<tr><td width="40%">Smacker</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used by many games.</td></tr>
<tr><td width="40%">SMJPEG</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in certain Loki game ports.</td></tr>
<tr><td width="40%">SMPTE 337M encapsulation</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Smush</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used in some LucasArts games.</td></tr>
<tr><td width="40%">Sony OpenMG (OMA)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Audio format used in Sony Sonic Stage and Sony Vegas.</td></tr>
<tr><td width="40%">Sony PlayStation STR</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sony Wave64 (W64)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SoX native format</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SUN AU format</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SUP raw PGS subtitles</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SVAG</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used in Konami PS2 games.</td></tr>
<tr><td width="40%">TDSC</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Text files</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">THP</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used on the Nintendo GameCube.</td></tr>
<tr><td width="40%">Tiertex Limited SEQ</td><td width="10%"></td><td width="10%">X</td><td width="40%">Tiertex .seq files used in the DOS CD-ROM version of the game Flashback.</td></tr>
<tr><td width="40%">True Audio</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">VAG</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used in many Sony PS2 games.</td></tr>
<tr><td width="40%">VC-1 test bitstream</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Vidvox Hap</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Vivo</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">VPK</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used in Sony PS games.</td></tr>
<tr><td width="40%">Marble WADY</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WAV</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Waveform Archiver</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WavPack</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">WebM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Televison (WTV)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Wing Commander III movie</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used in Origin&rsquo;s Wing Commander III computer game.</td></tr>
<tr><td width="40%">Westwood Studios audio</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Multimedia format used in Westwood Studios games.</td></tr>
<tr><td width="40%">Westwood Studios VQA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Multimedia format used in Westwood Studios games.</td></tr>
<tr><td width="40%">Wideband Single-bit Data (WSD)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WVE</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Konami XMD</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">XMV</td><td width="10%"></td><td width="10%">X</td><td width="40%">Microsoft video container used in Xbox games.</td></tr>
<tr><td width="40%">XVAG</td><td width="10%"></td><td width="10%">X</td><td width="40%">Audio format used on the PS3.</td></tr>
<tr><td width="40%">xWMA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Microsoft audio container used by XAudio 2.</td></tr>
<tr><td width="40%">eXtended BINary text (XBIN)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">YUV4MPEG pipe</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Psygnosis YOP</td><td width="10%"></td><td width="10%">X</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the feature in that column (encoding / decoding) is supported.
</p>
</div>
<div class="section-level-extent" id="Image-Formats">
<h3 class="section"><span>2.2 Image Formats<a class="copiable-link" href="#Image-Formats"> &para;</a></span></h3>

<p>FFmpeg can read and write images for each frame of a video sequence. The
following image formats are supported:
</p>
<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Encoding</td><td width="10%">Decoding</td><td width="40%">Comments</td></tr>
<tr><td width="40%">.Y.U.V</td><td width="10%">X</td><td width="10%">X</td><td width="40%">one raw file per component</td></tr>
<tr><td width="40%">Alias PIX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Alias/Wavefront PIX image format</td></tr>
<tr><td width="40%">animated GIF</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">APNG</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Animated Portable Network Graphics</td></tr>
<tr><td width="40%">BMP</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Microsoft BMP image</td></tr>
<tr><td width="40%">BRender PIX</td><td width="10%"></td><td width="10%">X</td><td width="40%">Argonaut BRender 3D engine image format.</td></tr>
<tr><td width="40%">CRI</td><td width="10%"></td><td width="10%">X</td><td width="40%">Cintel RAW</td></tr>
<tr><td width="40%">DPX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Digital Picture Exchange</td></tr>
<tr><td width="40%">EXR</td><td width="10%"></td><td width="10%">X</td><td width="40%">OpenEXR</td></tr>
<tr><td width="40%">FITS</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Flexible Image Transport System</td></tr>
<tr><td width="40%">HDR</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Radiance HDR RGBE Image format</td></tr>
<tr><td width="40%">IMG</td><td width="10%"></td><td width="10%">X</td><td width="40%">GEM Raster image</td></tr>
<tr><td width="40%">JPEG</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Progressive JPEG is not supported.</td></tr>
<tr><td width="40%">JPEG 2000</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">JPEG-LS</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">LJPEG</td><td width="10%">X</td><td width="10%"></td><td width="40%">Lossless JPEG</td></tr>
<tr><td width="40%">Media 100</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MSP</td><td width="10%"></td><td width="10%">X</td><td width="40%">Microsoft Paint image</td></tr>
<tr><td width="40%">PAM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">PAM is a PNM extension with alpha support.</td></tr>
<tr><td width="40%">PBM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable BitMap image</td></tr>
<tr><td width="40%">PCD</td><td width="10%"></td><td width="10%">X</td><td width="40%">PhotoCD</td></tr>
<tr><td width="40%">PCX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">PC Paintbrush</td></tr>
<tr><td width="40%">PFM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable FloatMap image</td></tr>
<tr><td width="40%">PGM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable GrayMap image</td></tr>
<tr><td width="40%">PGMYUV</td><td width="10%">X</td><td width="10%">X</td><td width="40%">PGM with U and V components in YUV 4:2:0</td></tr>
<tr><td width="40%">PGX</td><td width="10%"></td><td width="10%">X</td><td width="40%">PGX file decoder</td></tr>
<tr><td width="40%">PHM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable HalfFloatMap image</td></tr>
<tr><td width="40%">PIC</td><td width="10%"></td><td width="10%">X</td><td width="40%">Pictor/PC Paint</td></tr>
<tr><td width="40%">PNG</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable Network Graphics image</td></tr>
<tr><td width="40%">PPM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Portable PixelMap image</td></tr>
<tr><td width="40%">PSD</td><td width="10%"></td><td width="10%">X</td><td width="40%">Photoshop</td></tr>
<tr><td width="40%">PTX</td><td width="10%"></td><td width="10%">X</td><td width="40%">V.Flash PTX format</td></tr>
<tr><td width="40%">QOI</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Quite OK Image format</td></tr>
<tr><td width="40%">SGI</td><td width="10%">X</td><td width="10%">X</td><td width="40%">SGI RGB image format</td></tr>
<tr><td width="40%">Sun Rasterfile</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Sun RAS image format</td></tr>
<tr><td width="40%">TIFF</td><td width="10%">X</td><td width="10%">X</td><td width="40%">YUV, JPEG and some extension is not supported yet.</td></tr>
<tr><td width="40%">Truevision Targa</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Targa (.TGA) image format</td></tr>
<tr><td width="40%">VBN</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Vizrt Binary Image format</td></tr>
<tr><td width="40%">WBMP</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Wireless Application Protocol Bitmap image format</td></tr>
<tr><td width="40%">WebP</td><td width="10%">E</td><td width="10%">X</td><td width="40%">WebP image format, encoding supported through external library libwebp</td></tr>
<tr><td width="40%">XBM</td><td width="10%">X</td><td width="10%">X</td><td width="40%">X BitMap image format</td></tr>
<tr><td width="40%">XFace</td><td width="10%">X</td><td width="10%">X</td><td width="40%">X-Face image format</td></tr>
<tr><td width="40%">XPM</td><td width="10%"></td><td width="10%">X</td><td width="40%">X PixMap image format</td></tr>
<tr><td width="40%">XWD</td><td width="10%">X</td><td width="10%">X</td><td width="40%">X Window Dump image format</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the feature in that column (encoding / decoding) is supported.
</p>
<p><code class="code">E</code> means that support is provided through an external library.
</p>
</div>
<div class="section-level-extent" id="Video-Codecs">
<h3 class="section"><span>2.3 Video Codecs<a class="copiable-link" href="#Video-Codecs"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Encoding</td><td width="10%">Decoding</td><td width="40%">Comments</td></tr>
<tr><td width="40%">4X Movie</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in certain computer games.</td></tr>
<tr><td width="40%">8088flex TMV</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">A64 multicolor</td><td width="10%">X</td><td width="10%"></td><td width="40%">Creates video suitable to be played on a commodore 64 (multicolor mode).</td></tr>
<tr><td width="40%">Amazing Studio PAF Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">American Laser Games MM</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in games like Mad Dog McCree.</td></tr>
<tr><td width="40%">Amuse Graphics Movie</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">AMV Video</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Chinese MP3 players.</td></tr>
<tr><td width="40%">ANSI/ASCII art</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Apple Intermediate Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Apple MJPEG-B</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Apple Pixlet</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Apple ProRes</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: apch,apcn,apcs,apco,ap4h,ap4x</td></tr>
<tr><td width="40%">Apple QuickDraw</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: qdrw</td></tr>
<tr><td width="40%">Argonaut Video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some Argonaut games.</td></tr>
<tr><td width="40%">Asus v1</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: ASV1</td></tr>
<tr><td width="40%">Asus v2</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: ASV2</td></tr>
<tr><td width="40%">ATI VCR1</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VCR1</td></tr>
<tr><td width="40%">ATI VCR2</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VCR2</td></tr>
<tr><td width="40%">Auravision Aura</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Auravision Aura 2</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Autodesk Animator Flic video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Autodesk RLE</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: AASC</td></tr>
<tr><td width="40%">AV1</td><td width="10%">E</td><td width="10%">E</td><td width="40%">Supported through external libraries libaom, libdav1d, librav1e and libsvtav1</td></tr>
<tr><td width="40%">Avid 1:1 10-bit RGB Packer</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: AVrp</td></tr>
<tr><td width="40%">AVS (Audio Video Standard) video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Video encoding used by the Creature Shock game.</td></tr>
<tr><td width="40%">AVS2-P2/IEEE1857.4</td><td width="10%">E</td><td width="10%">E</td><td width="40%">Supported through external libraries libxavs2 and libdavs2</td></tr>
<tr><td width="40%">AVS3-P2/IEEE1857.10</td><td width="10%"></td><td width="10%">E</td><td width="40%">Supported through external library libuavs3d</td></tr>
<tr><td width="40%">AYUV</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Microsoft uncompressed packed 4:4:4:4</td></tr>
<tr><td width="40%">Beam Software VB</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Bethesda VID video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some games from Bethesda Softworks.</td></tr>
<tr><td width="40%">Bink Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">BitJazz SheerVideo</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Bitmap Brothers JV video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">y41p Brooktree uncompressed 4:1:1 12-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Brooktree ProSumer Video</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: BT20</td></tr>
<tr><td width="40%">Brute Force &amp; Ignorance</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in the game Flash Traffic: City of Angels.</td></tr>
<tr><td width="40%">C93 video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in Cyberia game.</td></tr>
<tr><td width="40%">CamStudio</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: CSCD</td></tr>
<tr><td width="40%">CD+G</td><td width="10%"></td><td width="10%">X</td><td width="40%">Video codec for CD+G karaoke disks</td></tr>
<tr><td width="40%">CDXL</td><td width="10%"></td><td width="10%">X</td><td width="40%">Amiga CD video codec</td></tr>
<tr><td width="40%">Chinese AVS video</td><td width="10%">E</td><td width="10%">X</td><td width="40%">AVS1-P2, JiZhun profile, encoding through external library libxavs</td></tr>
<tr><td width="40%">Delphine Software International CIN video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in Delphine Software International games.</td></tr>
<tr><td width="40%">Discworld II BMV Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">CineForm HD</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Canopus HQ</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Canopus HQA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Canopus HQX</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Canopus Lossless Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">CDToons</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in various Broderbund games.</td></tr>
<tr><td width="40%">Cinepak</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Cirrus Logic AccuPak</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: CLJR</td></tr>
<tr><td width="40%">CPiA Video Format</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Creative YUV (CYUV)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DFA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in Chronomaster game.</td></tr>
<tr><td width="40%">Dirac</td><td width="10%">E</td><td width="10%">X</td><td width="40%">supported though the native vc2 (Dirac Pro) encoder</td></tr>
<tr><td width="40%">Deluxe Paint Animation</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DNxHD</td><td width="10%">X</td><td width="10%">X</td><td width="40%">aka SMPTE VC3</td></tr>
<tr><td width="40%">Duck TrueMotion 1.0</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: DUCK</td></tr>
<tr><td width="40%">Duck TrueMotion 2.0</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: TM20</td></tr>
<tr><td width="40%">Duck TrueMotion 2.0 RT</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: TR20</td></tr>
<tr><td width="40%">DV (Digital Video)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Dxtory capture format</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Feeble Files/ScummVM DXA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec originally used in Feeble Files game.</td></tr>
<tr><td width="40%">Electronic Arts CMV video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in NHL 95 game.</td></tr>
<tr><td width="40%">Electronic Arts Madcow video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Electronic Arts TGV video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Electronic Arts TGQ video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Electronic Arts TQI video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Escape 124</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Escape 130</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">EVC / MPEG-5 Part 1</td><td width="10%">E</td><td width="10%">E</td><td width="40%">encoding and decoding supported through external libraries libxeve and libxevd</td></tr>
<tr><td width="40%">FFmpeg video codec #1</td><td width="10%">X</td><td width="10%">X</td><td width="40%">lossless codec (fourcc: FFV1)</td></tr>
<tr><td width="40%">Flash Screen Video v1</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: FSV1</td></tr>
<tr><td width="40%">Flash Screen Video v2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Flash Video (FLV)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Sorenson H.263 used in Flash</td></tr>
<tr><td width="40%">FM Screen Capture Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Forward Uncompressed</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Fraps</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Go2Meeting</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: G2M2, G2M3</td></tr>
<tr><td width="40%">Go2Webinar</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: G2M4</td></tr>
<tr><td width="40%">Gremlin Digital Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">H.261</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">H.263 / H.263-1996</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">H.263+ / H.263-1998 / H.263 version 2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libx264 and OpenH264</td></tr>
<tr><td width="40%">HEVC</td><td width="10%">X</td><td width="10%">X</td><td width="40%">encoding supported through external library libx265 and libkvazaar</td></tr>
<tr><td width="40%">HNM version 4</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">HuffYUV</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">HuffYUV FFmpeg variant</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">IBM Ultimotion</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: ULTI</td></tr>
<tr><td width="40%">id Cinematic video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Quake II.</td></tr>
<tr><td width="40%">id RoQ video</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Quake III, Jedi Knight 2, other computer games.</td></tr>
<tr><td width="40%">IFF ILBM</td><td width="10%"></td><td width="10%">X</td><td width="40%">IFF interleaved bitmap</td></tr>
<tr><td width="40%">IFF ByteRun1</td><td width="10%"></td><td width="10%">X</td><td width="40%">IFF run length encoded bitmap</td></tr>
<tr><td width="40%">Infinity IMM4</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Intel H.263</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Intel Indeo 2</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Intel Indeo 3</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Intel Indeo 4</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Intel Indeo 5</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Interplay C93</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in the game Cyberia from Interplay.</td></tr>
<tr><td width="40%">Interplay MVE video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Interplay .MVE files.</td></tr>
<tr><td width="40%">J2K</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Karl Morton&rsquo;s video codec</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in Worms games.</td></tr>
<tr><td width="40%">Kega Game Video (KGV1)</td><td width="10%"></td><td width="10%">X</td><td width="40%">Kega emulator screen capture codec.</td></tr>
<tr><td width="40%">Lagarith</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LCEVC / MPEG-5 LCEVC / MPEG-5 Part 2</td><td width="10%"></td><td width="10%">E</td><td width="40%">decoding supported through external library liblcevc-dec</td></tr>
<tr><td width="40%">LCL (LossLess Codec Library) MSZH</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LCL (LossLess Codec Library) ZLIB</td><td width="10%">E</td><td width="10%">E</td></tr>
<tr><td width="40%">LEAD MCMP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LOCO</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LucasArts SANM/Smush</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in LucasArts games / SMUSH animations.</td></tr>
<tr><td width="40%">lossless MJPEG</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MagicYUV Video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Mandsoft Screen Capture Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Microsoft ATC Screen</td><td width="10%"></td><td width="10%">X</td><td width="40%">Also known as Microsoft Screen 3.</td></tr>
<tr><td width="40%">Microsoft Expression Encoder Screen</td><td width="10%"></td><td width="10%">X</td><td width="40%">Also known as Microsoft Titanium Screen 2.</td></tr>
<tr><td width="40%">Microsoft RLE</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Microsoft Screen 1</td><td width="10%"></td><td width="10%">X</td><td width="40%">Also known as Windows Media Video V7 Screen.</td></tr>
<tr><td width="40%">Microsoft Screen 2</td><td width="10%"></td><td width="10%">X</td><td width="40%">Also known as Windows Media Video V9 Screen.</td></tr>
<tr><td width="40%">Microsoft Video 1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Mimic</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in MSN Messenger Webcam streams.</td></tr>
<tr><td width="40%">Miro VideoXL</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VIXL</td></tr>
<tr><td width="40%">MJPEG (Motion JPEG)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Mobotix MxPEG video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Motion Pixels video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-1 video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-2 video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-4 part 2</td><td width="10%">X</td><td width="10%">X</td><td width="40%">libxvidcore can be used alternatively for encoding.</td></tr>
<tr><td width="40%">MPEG-4 part 2 Microsoft variant version 1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-4 part 2 Microsoft variant version 2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG-4 part 2 Microsoft variant version 3</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Newtek SpeedHQ</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Nintendo Gamecube THP video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">NotchLC</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">NuppelVideo/RTjpeg</td><td width="10%"></td><td width="10%">X</td><td width="40%">Video encoding used in NuppelVideo files.</td></tr>
<tr><td width="40%">On2 VP3</td><td width="10%"></td><td width="10%">X</td><td width="40%">still experimental</td></tr>
<tr><td width="40%">On2 VP4</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VP40</td></tr>
<tr><td width="40%">On2 VP5</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VP50</td></tr>
<tr><td width="40%">On2 VP6</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VP60,VP61,VP62</td></tr>
<tr><td width="40%">On2 VP7</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: VP70,VP71</td></tr>
<tr><td width="40%">VP8</td><td width="10%">E</td><td width="10%">X</td><td width="40%">fourcc: VP80, encoding supported through external library libvpx</td></tr>
<tr><td width="40%">VP9</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libvpx</td></tr>
<tr><td width="40%">Pinnacle TARGA CineWave YUV16</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: Y216</td></tr>
<tr><td width="40%">Q-team QPEG</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourccs: QPEG, Q1.0, Q1.1</td></tr>
<tr><td width="40%">QuickTime 8BPS video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">QuickTime Animation (RLE) video</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: &rsquo;rle &rsquo;</td></tr>
<tr><td width="40%">QuickTime Graphics (SMC)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: &rsquo;smc &rsquo;</td></tr>
<tr><td width="40%">QuickTime video (RPZA)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: rpza</td></tr>
<tr><td width="40%">R10K AJA Kona 10-bit RGB Codec</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">R210 Quicktime Uncompressed RGB 10-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Raw Video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RealVideo 1.0</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RealVideo 2.0</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">RealVideo 3.0</td><td width="10%"></td><td width="10%">X</td><td width="40%">still far from ideal</td></tr>
<tr><td width="40%">RealVideo 4.0</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Renderware TXD (TeXture Dictionary)</td><td width="10%"></td><td width="10%">X</td><td width="40%">Texture dictionaries used by the Renderware Engine.</td></tr>
<tr><td width="40%">RivaTuner Video</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: &rsquo;RTV1&rsquo;</td></tr>
<tr><td width="40%">RL2 video</td><td width="10%"></td><td width="10%">X</td><td width="40%">used in some games by Entertainment Software Partners</td></tr>
<tr><td width="40%">ScreenPressor</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Screenpresso</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Screen Recorder Gold Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sierra VMD video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sierra VMD files.</td></tr>
<tr><td width="40%">Silicon Graphics Motion Video Compressor 1 (MVC1)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Silicon Graphics Motion Video Compressor 2 (MVC2)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Silicon Graphics RLE 8-bit video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Smacker video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Video encoding used in Smacker.</td></tr>
<tr><td width="40%">SMPTE VC-1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Snow</td><td width="10%">X</td><td width="10%">X</td><td width="40%">experimental wavelet codec (fourcc: SNOW)</td></tr>
<tr><td width="40%">Sony PlayStation MDEC (Motion DECoder)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sorenson Vector Quantizer 1</td><td width="10%">X</td><td width="10%">X</td><td width="40%">fourcc: SVQ1</td></tr>
<tr><td width="40%">Sorenson Vector Quantizer 3</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: SVQ3</td></tr>
<tr><td width="40%">Sunplus JPEG (SP5X)</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: SP5X</td></tr>
<tr><td width="40%">TechSmith Screen Capture Codec</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: TSCC</td></tr>
<tr><td width="40%">TechSmith Screen Capture Codec 2</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: TSC2</td></tr>
<tr><td width="40%">Theora</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libtheora</td></tr>
<tr><td width="40%">Tiertex Limited SEQ video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in DOS CD-ROM FlashBack game.</td></tr>
<tr><td width="40%">Ut Video</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">v210 QuickTime uncompressed 4:2:2 10-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">v308 QuickTime uncompressed 4:4:4</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">v408 QuickTime uncompressed 4:4:4:4</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">v410 QuickTime uncompressed 4:4:4 10-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">VBLE Lossless Codec</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">vMix Video</td><td width="10%"></td><td width="10%">X</td><td width="40%">fourcc: &rsquo;VMX1&rsquo;</td></tr>
<tr><td width="40%">VMware Screen Codec / VMware Video</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in videos captured by VMware.</td></tr>
<tr><td width="40%">Westwood Studios VQA (Vector Quantized Animation) video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Image</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Video 7</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Video 8</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Video 9</td><td width="10%"></td><td width="10%">X</td><td width="40%">not completely working</td></tr>
<tr><td width="40%">Wing Commander III / Xan</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Wing Commander III .MVE files.</td></tr>
<tr><td width="40%">Wing Commander IV / Xan</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Wing Commander IV.</td></tr>
<tr><td width="40%">Winnov WNV1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WMV7</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">YAMAHA SMAF</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Psygnosis YOP Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">yuv4</td><td width="10%">X</td><td width="10%">X</td><td width="40%">libquicktime uncompressed packed 4:2:0</td></tr>
<tr><td width="40%">ZeroCodec Lossless Video</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ZLIB</td><td width="10%">X</td><td width="10%">X</td><td width="40%">part of LCL, encoder experimental</td></tr>
<tr><td width="40%">Zip Motion Blocks Video</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Encoder works only in PAL8.</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the feature in that column (encoding / decoding) is supported.
</p>
<p><code class="code">E</code> means that support is provided through an external library.
</p>
</div>
<div class="section-level-extent" id="Audio-Codecs">
<h3 class="section"><span>2.4 Audio Codecs<a class="copiable-link" href="#Audio-Codecs"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Encoding</td><td width="10%">Decoding</td><td width="40%">Comments</td></tr>
<tr><td width="40%">8SVX exponential</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">8SVX fibonacci</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">AAC</td><td width="10%">EX</td><td width="10%">X</td><td width="40%">encoding supported through internal encoder and external library libfdk-aac</td></tr>
<tr><td width="40%">AAC+</td><td width="10%">E</td><td width="10%">IX</td><td width="40%">encoding supported through external library libfdk-aac</td></tr>
<tr><td width="40%">AC-3</td><td width="10%">IX</td><td width="10%">IX</td></tr>
<tr><td width="40%">ACELP.KELVIN</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM 4X Movie</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Yamaha AICA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM AmuseGraphics Movie</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Argonaut Games</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM CDROM XA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Creative Technology</td><td width="10%"></td><td width="10%">X</td><td width="40%">16 -&gt; 4, 8 -&gt; 4, 8 -&gt; 3, 8 -&gt; 2</td></tr>
<tr><td width="40%">ADPCM Electronic Arts</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in various EA titles.</td></tr>
<tr><td width="40%">ADPCM Electronic Arts Maxis CDROM XS</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sim City 3000.</td></tr>
<tr><td width="40%">ADPCM Electronic Arts R1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Electronic Arts R2</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Electronic Arts R3</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Electronic Arts XAS</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM G.722</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM G.726</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Acorn Replay</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA AMV</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in AMV files</td></tr>
<tr><td width="40%">ADPCM IMA Cunning Developments</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Electronic Arts EACS</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Electronic Arts SEAD</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Funcom</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA High Voltage Software ALP</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Mobiclip MOFLEX</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA QuickTime</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Simon &amp; Schuster Interactive</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Ubisoft APM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Loki SDL MJPEG</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA WAV</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Westwood</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM ISS IMA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in FunCom games.</td></tr>
<tr><td width="40%">ADPCM IMA Dialogic</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM IMA Duck DK3</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some Sega Saturn console games.</td></tr>
<tr><td width="40%">ADPCM IMA Duck DK4</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in some Sega Saturn console games.</td></tr>
<tr><td width="40%">ADPCM IMA Radical</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Microsoft</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM MS IMA</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Nintendo Gamecube AFC</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Nintendo Gamecube DTK</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Nintendo THP</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Playstation</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM QT IMA</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM SEGA CRI ADX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Sega Dreamcast games.</td></tr>
<tr><td width="40%">ADPCM Shockwave Flash</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Sound Blaster Pro 2-bit</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Sound Blaster Pro 2.6-bit</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Sound Blaster Pro 4-bit</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM VIMA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in LucasArts SMUSH animations.</td></tr>
<tr><td width="40%">ADPCM Konami XMD</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Westwood Studios IMA</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Westwood Studios games like Command and Conquer.</td></tr>
<tr><td width="40%">ADPCM Yamaha</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">ADPCM Zork</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">AMR-NB</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libopencore-amrnb</td></tr>
<tr><td width="40%">AMR-WB</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libvo-amrwbenc</td></tr>
<tr><td width="40%">Amazing Studio PAF Audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Apple lossless audio</td><td width="10%">X</td><td width="10%">X</td><td width="40%">QuickTime fourcc &rsquo;alac&rsquo;</td></tr>
<tr><td width="40%">aptX</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Bluetooth A2DP</td></tr>
<tr><td width="40%">aptX HD</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Bluetooth A2DP</td></tr>
<tr><td width="40%">ATRAC1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ATRAC3</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ATRAC3+</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">ATRAC9</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Bink Audio</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Bink and Smacker files in many games.</td></tr>
<tr><td width="40%">Bonk audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">CELT</td><td width="10%"></td><td width="10%">E</td><td width="40%">decoding supported through external library libcelt</td></tr>
<tr><td width="40%">codec2</td><td width="10%">E</td><td width="10%">E</td><td width="40%">en/decoding supported through external library libcodec2</td></tr>
<tr><td width="40%">CRI HCA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Delphine Software International CIN audio</td><td width="10%"></td><td width="10%">X</td><td width="40%">Codec used in Delphine Software International games.</td></tr>
<tr><td width="40%">DFPWM</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Digital Speech Standard - Standard Play mode (DSS SP)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Discworld II BMV Audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">COOK</td><td width="10%"></td><td width="10%">X</td><td width="40%">All versions except 5.1 are supported.</td></tr>
<tr><td width="40%">DCA (DTS Coherent Acoustics)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">supported extensions: XCh, XXCH, X96, XBR, XLL, LBR (partially)</td></tr>
<tr><td width="40%">Dolby E</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DPCM Cuberoot-Delta-Exact</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in few games.</td></tr>
<tr><td width="40%">DPCM Gremlin</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DPCM id RoQ</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Quake III, Jedi Knight 2 and other computer games.</td></tr>
<tr><td width="40%">DPCM Marble WADY</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DPCM Interplay</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in various Interplay computer games.</td></tr>
<tr><td width="40%">DPCM Squareroot-Delta-Exact</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in various games.</td></tr>
<tr><td width="40%">DPCM Sierra Online</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sierra Online game audio files.</td></tr>
<tr><td width="40%">DPCM Sol</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DPCM Xan</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Origin&rsquo;s Wing Commander IV AVI files.</td></tr>
<tr><td width="40%">DPCM Xilam DERF</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSD (Direct Stream Digital), least significant bit first</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSD (Direct Stream Digital), most significant bit first</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSD (Direct Stream Digital), least significant bit first, planar</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSD (Direct Stream Digital), most significant bit first, planar</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DSP Group TrueSpeech</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DST (Direct Stream Transfer)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DV audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Enhanced AC-3</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">EVRC (Enhanced Variable Rate Codec)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">FLAC (Free Lossless Audio Codec)</td><td width="10%">X</td><td width="10%">IX</td></tr>
<tr><td width="40%">FTR Voice</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">G.723.1</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">G.729</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">GSM</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libgsm</td></tr>
<tr><td width="40%">GSM Microsoft variant</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libgsm</td></tr>
<tr><td width="40%">IAC (Indeo Audio Coder)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">iLBC (Internet Low Bitrate Codec)</td><td width="10%">E</td><td width="10%">EX</td><td width="40%">encoding and decoding supported through external library libilbc</td></tr>
<tr><td width="40%">IMC (Intel Music Coder)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Interplay ACM</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">LC3</td><td width="10%">E</td><td width="10%">E</td><td width="40%">supported through external library liblc3</td></tr>
<tr><td width="40%">MACE (Macintosh Audio Compression/Expansion) 6:1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Marian&rsquo;s A-pac audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MI-SC4 (Micronas SC-4 Audio)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MLP (Meridian Lossless Packing)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in DVD-Audio discs.</td></tr>
<tr><td width="40%">Monkey&rsquo;s Audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MP1 (MPEG audio layer 1)</td><td width="10%"></td><td width="10%">IX</td></tr>
<tr><td width="40%">MP2 (MPEG audio layer 2)</td><td width="10%">IX</td><td width="10%">IX</td><td width="40%">encoding supported also through external library TwoLAME</td></tr>
<tr><td width="40%">MP3 (MPEG audio layer 3)</td><td width="10%">E</td><td width="10%">IX</td><td width="40%">encoding supported through external library LAME, ADU MP3 and MP3onMP4 also supported</td></tr>
<tr><td width="40%">MPEG-4 Audio Lossless Coding (ALS)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MobiClip FastAudio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Musepack SV7</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Musepack SV8</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Nellymoser Asao</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">On2 AVC (Audio for Video Codec)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Opus</td><td width="10%">E</td><td width="10%">X</td><td width="40%">encoding supported through external library libopus</td></tr>
<tr><td width="40%">OSQ (Original Sound Quality)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">PCM A-law</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM mu-law</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM Archimedes VIDC</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 8-bit planar</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 16-bit big-endian planar</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 16-bit little-endian planar</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 24-bit little-endian planar</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 32-bit little-endian planar</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM 32-bit floating point big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM 32-bit floating point little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM 64-bit floating point big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM 64-bit floating point little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM D-Cinema audio signed 24-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 8-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 16-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 16-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 24-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 24-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 32-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 32-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM signed 16/20/24-bit big-endian in MPEG-TS</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 8-bit</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 16-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 16-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 24-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 24-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 32-bit big-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM unsigned 32-bit little-endian</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PCM SGA</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">QCELP / PureVoice</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">QDesign Music Codec 1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">QDesign Music Codec 2</td><td width="10%"></td><td width="10%">X</td><td width="40%">There are still some distortions.</td></tr>
<tr><td width="40%">RealAudio 1.0 (14.4K)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Real 14400 bit/s codec</td></tr>
<tr><td width="40%">RealAudio 2.0 (28.8K)</td><td width="10%"></td><td width="10%">X</td><td width="40%">Real 28800 bit/s codec</td></tr>
<tr><td width="40%">RealAudio 3.0 (dnet)</td><td width="10%">IX</td><td width="10%">X</td><td width="40%">Real low bitrate AC-3 codec</td></tr>
<tr><td width="40%">RealAudio Lossless</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RealAudio SIPR / ACELP.NET</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RK Audio (RKA)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SBC (low-complexity subband codec)</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in Bluetooth A2DP</td></tr>
<tr><td width="40%">Shorten</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Sierra VMD audio</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in Sierra VMD files.</td></tr>
<tr><td width="40%">Smacker audio</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SMPTE 302M AES3 audio</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Sonic</td><td width="10%">X</td><td width="10%">X</td><td width="40%">experimental codec</td></tr>
<tr><td width="40%">Sonic lossless</td><td width="10%">X</td><td width="10%">X</td><td width="40%">experimental codec</td></tr>
<tr><td width="40%">Speex</td><td width="10%">E</td><td width="10%">EX</td><td width="40%">supported through external library libspeex</td></tr>
<tr><td width="40%">TAK (Tom&rsquo;s lossless Audio Kompressor)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">True Audio (TTA)</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">TrueHD</td><td width="10%">X</td><td width="10%">X</td><td width="40%">Used in HD-DVD and Blu-Ray discs.</td></tr>
<tr><td width="40%">TwinVQ (VQF flavor)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">VIMA</td><td width="10%"></td><td width="10%">X</td><td width="40%">Used in LucasArts SMUSH animations.</td></tr>
<tr><td width="40%">ViewQuest VQC</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Vorbis</td><td width="10%">E</td><td width="10%">X</td><td width="40%">A native but very primitive encoder exists.</td></tr>
<tr><td width="40%">Voxware MetaSound</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Waveform Archiver</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WavPack</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Westwood Audio (SND1)</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Audio 1</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Audio 2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Audio Lossless</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Audio Pro</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Windows Media Audio Voice</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Xbox Media Audio 1</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Xbox Media Audio 2</td><td width="10%"></td><td width="10%">X</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the feature in that column (encoding / decoding) is supported.
</p>
<p><code class="code">E</code> means that support is provided through an external library.
</p>
<p><code class="code">I</code> means that an integer-only version is available, too (ensures high
performance on systems without hardware floating point support).
</p>
</div>
<div class="section-level-extent" id="Subtitle-Formats">
<h3 class="section"><span>2.5 Subtitle Formats<a class="copiable-link" href="#Subtitle-Formats"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Muxing</td><td width="10%">Demuxing</td><td width="10%">Encoding</td><td width="10%">Decoding</td></tr>
<tr><td width="40%">3GPP Timed Text</td><td width="10%"></td><td width="10%"></td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">AQTitle</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DVB</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">DVB teletext</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">E</td></tr>
<tr><td width="40%">DVD</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">JACOsub</td><td width="10%">X</td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MicroDVD</td><td width="10%">X</td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MPL2</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">MPsub (MPlayer)</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">PGS</td><td width="10%"></td><td width="10%"></td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">PJS (Phoenix)</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">RealText</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SAMI</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Spruce format (STL)</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SSA/ASS</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SubRip (SRT)</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SubViewer v1</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">SubViewer</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">TED Talks captions</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">TTML</td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">VobSub (IDX+SUB)</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">VPlayer</td><td width="10%"></td><td width="10%">X</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">WebVTT</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">XSUB</td><td width="10%"></td><td width="10%"></td><td width="10%">X</td><td width="10%">X</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the feature is supported.
</p>
<p><code class="code">E</code> means that support is provided through an external library.
</p>
</div>
<div class="section-level-extent" id="Network-Protocols">
<h3 class="section"><span>2.6 Network Protocols<a class="copiable-link" href="#Network-Protocols"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Support</td></tr>
<tr><td width="40%">AMQP</td><td width="10%">E</td></tr>
<tr><td width="40%">file</td><td width="10%">X</td></tr>
<tr><td width="40%">FTP</td><td width="10%">X</td></tr>
<tr><td width="40%">Gopher</td><td width="10%">X</td></tr>
<tr><td width="40%">Gophers</td><td width="10%">X</td></tr>
<tr><td width="40%">HLS</td><td width="10%">X</td></tr>
<tr><td width="40%">HTTP</td><td width="10%">X</td></tr>
<tr><td width="40%">HTTPS</td><td width="10%">X</td></tr>
<tr><td width="40%">Icecast</td><td width="10%">X</td></tr>
<tr><td width="40%">MMSH</td><td width="10%">X</td></tr>
<tr><td width="40%">MMST</td><td width="10%">X</td></tr>
<tr><td width="40%">pipe</td><td width="10%">X</td></tr>
<tr><td width="40%">Pro-MPEG FEC</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMP</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMPE</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMPS</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMPT</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMPTE</td><td width="10%">X</td></tr>
<tr><td width="40%">RTMPTS</td><td width="10%">X</td></tr>
<tr><td width="40%">RTP</td><td width="10%">X</td></tr>
<tr><td width="40%">SAMBA</td><td width="10%">E</td></tr>
<tr><td width="40%">SCTP</td><td width="10%">X</td></tr>
<tr><td width="40%">SFTP</td><td width="10%">E</td></tr>
<tr><td width="40%">TCP</td><td width="10%">X</td></tr>
<tr><td width="40%">TLS</td><td width="10%">X</td></tr>
<tr><td width="40%">UDP</td><td width="10%">X</td></tr>
<tr><td width="40%">ZMQ</td><td width="10%">E</td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that the protocol is supported.
</p>
<p><code class="code">E</code> means that support is provided through an external library.
</p>

</div>
<div class="section-level-extent" id="Input_002fOutput-Devices">
<h3 class="section"><span>2.7 Input/Output Devices<a class="copiable-link" href="#Input_002fOutput-Devices"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Name</td><td width="10%">Input</td><td width="10%">Output</td></tr>
<tr><td width="40%">ALSA</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">BKTR</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">caca</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">DV1394</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">Lavfi virtual device</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">Linux framebuffer</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">JACK</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">LIBCDIO</td><td width="10%">X</td></tr>
<tr><td width="40%">LIBDC1394</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">OpenAL</td><td width="10%">X</td></tr>
<tr><td width="40%">OpenGL</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">OSS</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">PulseAudio</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">SDL</td><td width="10%"></td><td width="10%">X</td></tr>
<tr><td width="40%">Video4Linux2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">VfW capture</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">X11 grabbing</td><td width="10%">X</td><td width="10%"></td></tr>
<tr><td width="40%">Win32 grabbing</td><td width="10%">X</td><td width="10%"></td></tr>
</tbody>
</table>

<p><code class="code">X</code> means that input/output is supported.
</p>
</div>
<div class="section-level-extent" id="Timecode">
<h3 class="section"><span>2.8 Timecode<a class="copiable-link" href="#Timecode"> &para;</a></span></h3>

<table class="multitable">
<tbody><tr><td width="40%">Codec/format</td><td width="10%">Read</td><td width="10%">Write</td></tr>
<tr><td width="40%">AVI</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">DV</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">GXF</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MOV</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MPEG1/2</td><td width="10%">X</td><td width="10%">X</td></tr>
<tr><td width="40%">MXF</td><td width="10%">X</td><td width="10%">X</td></tr>
</tbody>
</table>

</div>
</div>
</div>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
