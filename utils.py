"""
工具函数模块
包含文件处理、URL验证、系统检查等工具函数
"""

import os
import re
import sys
import shutil
import subprocess
from pathlib import Path
from urllib.parse import urlparse, parse_qs
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def validate_youtube_url(url):
    """
    验证YouTube URL是否有效
    
    Args:
        url: 待验证的URL
        
    Returns:
        tuple: (是否有效, 标准化的URL或错误信息)
    """
    if not url or not isinstance(url, str):
        return False, "URL不能为空"
    
    # YouTube URL的正则表达式模式
    youtube_patterns = [
        r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtube\.com/v/([a-zA-Z0-9_-]{11})',
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            video_id = match.group(1)
            standard_url = f"https://www.youtube.com/watch?v={video_id}"
            return True, standard_url
    
    return False, "不是有效的YouTube URL"


def extract_video_id(url):
    """
    从YouTube URL中提取视频ID
    
    Args:
        url: YouTube URL
        
    Returns:
        视频ID或None
    """
    is_valid, result = validate_youtube_url(url)
    if is_valid:
        parsed = urlparse(result)
        query_params = parse_qs(parsed.query)
        return query_params.get('v', [None])[0]
    return None


def check_disk_space(path, required_mb=100):
    """
    检查磁盘空间是否足够
    
    Args:
        path: 检查路径
        required_mb: 需要的空间(MB)
        
    Returns:
        tuple: (是否足够, 可用空间MB)
    """
    try:
        if sys.platform == 'win32':
            free_bytes = shutil.disk_usage(path).free
        else:
            statvfs = os.statvfs(path)
            free_bytes = statvfs.f_frsize * statvfs.f_bavail
        
        free_mb = free_bytes / (1024 * 1024)
        return free_mb >= required_mb, int(free_mb)
    except Exception as e:
        logger.error(f"检查磁盘空间失败: {e}")
        return False, 0


def get_ffmpeg_path():
    """
    获取FFmpeg可执行文件路径

    Returns:
        FFmpeg可执行文件的完整路径，如果找不到则返回None
    """
    # 首先检查本地ffmpeg目录
    local_ffmpeg = Path("ffmpeg/bin/ffmpeg.exe")
    if local_ffmpeg.exists():
        return str(local_ffmpeg.absolute())

    # 检查系统PATH中的ffmpeg
    try:
        result = subprocess.run(
            ['where', 'ffmpeg'] if sys.platform == 'win32' else ['which', 'ffmpeg'],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0 and result.stdout.strip():
            return result.stdout.strip().split('\n')[0]
    except:
        pass

    return None


def check_ffmpeg():
    """
    检查FFmpeg是否可用

    Returns:
        tuple: (是否可用, 版本信息或错误信息, FFmpeg路径)
    """
    ffmpeg_path = get_ffmpeg_path()

    if not ffmpeg_path:
        return False, "FFmpeg未找到（请确保ffmpeg/bin/ffmpeg.exe存在或FFmpeg在系统PATH中）", None

    try:
        result = subprocess.run(
            [ffmpeg_path, '-version'],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            return True, version_line, ffmpeg_path
        else:
            return False, "FFmpeg命令执行失败", ffmpeg_path
    except subprocess.TimeoutExpired:
        return False, "FFmpeg命令超时", ffmpeg_path
    except FileNotFoundError:
        return False, f"FFmpeg文件不存在: {ffmpeg_path}", ffmpeg_path
    except Exception as e:
        return False, f"检查FFmpeg时出错: {e}", ffmpeg_path


def format_file_size(size_bytes):
    """
    格式化文件大小显示
    
    Args:
        size_bytes: 文件大小(字节)
        
    Returns:
        格式化的大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def format_duration(seconds):
    """
    格式化时长显示
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时长字符串
    """
    if seconds < 60:
        return f"{int(seconds)}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes}分{secs}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours}小时{minutes}分{secs}秒"


def clean_temp_files(temp_dir, max_age_hours=24):
    """
    清理临时文件
    
    Args:
        temp_dir: 临时文件目录
        max_age_hours: 文件最大保留时间(小时)
        
    Returns:
        清理的文件数量
    """
    temp_dir = Path(temp_dir)
    if not temp_dir.exists():
        return 0
    
    import time
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    cleaned_count = 0
    
    try:
        for file_path in temp_dir.iterdir():
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    cleaned_count += 1
                    logger.info(f"清理临时文件: {file_path.name}")
    except Exception as e:
        logger.error(f"清理临时文件时出错: {e}")
    
    return cleaned_count


def ensure_directories(*dirs):
    """
    确保目录存在，不存在则创建
    
    Args:
        *dirs: 目录路径列表
        
    Returns:
        创建的目录数量
    """
    created_count = 0
    for dir_path in dirs:
        dir_path = Path(dir_path)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                created_count += 1
                logger.info(f"创建目录: {dir_path}")
            except Exception as e:
                logger.error(f"创建目录失败 {dir_path}: {e}")
    
    return created_count


def get_file_info(file_path):
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件信息字典
    """
    file_path = Path(file_path)
    if not file_path.exists():
        return None
    
    try:
        stat = file_path.stat()
        return {
            'name': file_path.name,
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'modified': stat.st_mtime,
            'is_file': file_path.is_file(),
            'is_dir': file_path.is_dir(),
            'extension': file_path.suffix.lower()
        }
    except Exception as e:
        logger.error(f"获取文件信息失败 {file_path}: {e}")
        return None


def check_system_requirements():
    """
    检查系统要求

    Returns:
        系统检查结果字典
    """
    ffmpeg_ok, ffmpeg_info, ffmpeg_path = check_ffmpeg()
    results = {
        'python_version': sys.version,
        'platform': sys.platform,
        'ffmpeg': (ffmpeg_ok, ffmpeg_info),
        'ffmpeg_path': ffmpeg_path,
        'disk_space': {},
        'directories': {}
    }
    
    # 检查各目录的磁盘空间
    for dir_name in ['mp3', 'txt', 'temp']:
        if Path(dir_name).exists():
            enough, free_mb = check_disk_space(dir_name)
            results['disk_space'][dir_name] = {
                'enough': enough,
                'free_mb': free_mb
            }
    
    # 检查目录是否存在
    for dir_name in ['mp3', 'txt', 'temp']:
        results['directories'][dir_name] = Path(dir_name).exists()
    
    return results


def print_system_info():
    """打印系统信息"""
    print("=" * 50)
    print("系统环境检查")
    print("=" * 50)
    
    info = check_system_requirements()
    
    print(f"Python版本: {info['python_version'].split()[0]}")
    print(f"操作系统: {info['platform']}")
    
    # FFmpeg检查
    ffmpeg_ok, ffmpeg_info = info['ffmpeg']
    print(f"FFmpeg: {'✓' if ffmpeg_ok else '✗'} {ffmpeg_info}")
    
    # 目录检查
    print("\n目录状态:")
    for dir_name, exists in info['directories'].items():
        print(f"  {dir_name}/: {'✓' if exists else '✗'}")
    
    # 磁盘空间检查
    print("\n磁盘空间:")
    for dir_name, space_info in info['disk_space'].items():
        status = '✓' if space_info['enough'] else '✗'
        print(f"  {dir_name}/: {status} {space_info['free_mb']} MB可用")
    
    print("=" * 50)


def main():
    """测试函数"""
    print("工具函数测试")
    
    # 测试URL验证
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ",
        "invalid_url",
        ""
    ]
    
    print("\nURL验证测试:")
    for url in test_urls:
        is_valid, result = validate_youtube_url(url)
        print(f"  {url}: {'✓' if is_valid else '✗'} {result}")
    
    # 系统信息检查
    print_system_info()


if __name__ == "__main__":
    main()
