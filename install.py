"""
安装脚本
帮助用户安装项目依赖和检查环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, check=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            check=check
        )
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr
    except Exception as e:
        return False, "", str(e)


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True


def install_pip_packages():
    """安装Python包"""
    print("\n安装Python依赖包...")
    
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install -r requirements.txt")
    
    if success:
        print("✅ Python依赖包安装成功")
        return True
    else:
        print("❌ Python依赖包安装失败")
        print(f"错误信息: {stderr}")
        return False


def check_ffmpeg():
    """检查FFmpeg安装"""
    print("\n检查FFmpeg...")
    success, stdout, stderr = run_command("ffmpeg -version", check=False)
    
    if success:
        version_line = stdout.split('\n')[0] if stdout else "未知版本"
        print(f"✅ FFmpeg已安装: {version_line}")
        return True
    else:
        print("❌ FFmpeg未安装")
        return False


def install_ffmpeg_windows():
    """Windows下安装FFmpeg的说明"""
    print("\n=== Windows FFmpeg 安装说明 ===")
    print("1. 访问 https://ffmpeg.org/download.html")
    print("2. 点击 'Windows' 选项")
    print("3. 选择 'Windows builds by BtbN' 或其他构建版本")
    print("4. 下载适合您系统的版本 (通常是64位)")
    print("5. 解压到一个目录，如 C:\\ffmpeg")
    print("6. 将 C:\\ffmpeg\\bin 添加到系统PATH环境变量")
    print("7. 重启命令行窗口")
    print("\n或者使用包管理器:")
    print("- 如果安装了Chocolatey: choco install ffmpeg")
    print("- 如果安装了Scoop: scoop install ffmpeg")


def install_ffmpeg_macos():
    """macOS下安装FFmpeg"""
    print("\n=== macOS FFmpeg 安装 ===")
    print("推荐使用Homebrew安装:")
    
    # 检查是否安装了Homebrew
    success, _, _ = run_command("brew --version", check=False)
    if success:
        print("检测到Homebrew，正在安装FFmpeg...")
        success, stdout, stderr = run_command("brew install ffmpeg")
        if success:
            print("✅ FFmpeg安装成功")
            return True
        else:
            print(f"❌ FFmpeg安装失败: {stderr}")
    else:
        print("未检测到Homebrew，请先安装Homebrew:")
        print('/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"')
        print("然后运行: brew install ffmpeg")
    
    return False


def install_ffmpeg_linux():
    """Linux下安装FFmpeg"""
    print("\n=== Linux FFmpeg 安装 ===")
    
    # 检测Linux发行版
    try:
        with open('/etc/os-release', 'r') as f:
            os_info = f.read().lower()
    except:
        os_info = ""
    
    if 'ubuntu' in os_info or 'debian' in os_info:
        print("检测到Ubuntu/Debian系统，正在安装FFmpeg...")
        success, stdout, stderr = run_command("sudo apt update && sudo apt install -y ffmpeg")
        if success:
            print("✅ FFmpeg安装成功")
            return True
        else:
            print(f"❌ FFmpeg安装失败: {stderr}")
            print("请手动运行: sudo apt install ffmpeg")
    
    elif 'centos' in os_info or 'rhel' in os_info or 'fedora' in os_info:
        print("检测到CentOS/RHEL/Fedora系统")
        print("请手动安装FFmpeg:")
        print("- CentOS/RHEL: sudo yum install ffmpeg")
        print("- Fedora: sudo dnf install ffmpeg")
    
    else:
        print("未能识别Linux发行版，请根据您的系统手动安装FFmpeg")
    
    return False


def setup_directories():
    """创建必要的目录"""
    print("\n创建项目目录...")
    dirs = ['mp3', 'txt', 'temp']
    
    for dir_name in dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"✅ 创建目录: {dir_name}/")
        else:
            print(f"✅ 目录已存在: {dir_name}/")


def main():
    """主安装流程"""
    print("=" * 60)
    print("    YouTube 音频下载和转录工具 - 安装脚本")
    print("=" * 60)
    
    # 1. 检查Python版本
    if not check_python_version():
        print("\n请升级Python版本后重新运行此脚本")
        return
    
    # 2. 创建目录
    setup_directories()
    
    # 3. 安装Python包
    if not install_pip_packages():
        print("\n请检查网络连接或手动安装依赖包:")
        print("pip install -r requirements.txt")
        return
    
    # 4. 检查和安装FFmpeg
    if not check_ffmpeg():
        system = platform.system().lower()
        
        if system == "windows":
            install_ffmpeg_windows()
        elif system == "darwin":  # macOS
            install_ffmpeg_macos()
        elif system == "linux":
            install_ffmpeg_linux()
        else:
            print(f"❌ 不支持的操作系统: {system}")
    
    # 5. 最终检查
    print("\n" + "=" * 60)
    print("安装完成检查:")
    print("=" * 60)
    
    # 检查关键模块是否可以导入
    modules_to_check = [
        ('yt_dlp', 'yt-dlp'),
        ('whisper', 'openai-whisper'),
        ('ffmpeg', 'ffmpeg-python')
    ]
    
    all_good = True
    for module, package in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {package} 可用")
        except ImportError:
            print(f"❌ {package} 不可用")
            all_good = False
    
    # 再次检查FFmpeg
    if check_ffmpeg():
        print("✅ FFmpeg 可用")
    else:
        print("❌ FFmpeg 不可用")
        all_good = False
    
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 所有依赖安装成功！")
        print("现在可以运行: python main.py")
    else:
        print("⚠️  部分依赖安装失败，请检查上述错误信息")
        print("您可以尝试手动安装缺失的依赖")
    print("=" * 60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
    except Exception as e:
        print(f"\n安装过程中出现错误: {e}")
        print("请检查错误信息并重试")
