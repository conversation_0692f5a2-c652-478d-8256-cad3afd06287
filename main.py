"""
YouTube音频下载和转录主程序
整合下载和转录功能，提供用户交互界面
"""

import sys
import os
from pathlib import Path
import logging
from datetime import datetime

# 导入自定义模块
from downloader import YouTubeDownloader
from transcriber import AudioTranscriber
from utils import (
    validate_youtube_url, 
    check_system_requirements, 
    print_system_info,
    clean_temp_files,
    ensure_directories,
    format_duration
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('youtube_downloader.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class YouTubeProcessor:
    """YouTube视频处理器主类"""
    
    def __init__(self):
        """初始化处理器"""
        self.downloader = None
        self.transcriber = None
        self.setup_directories()
        self.setup_components()
    
    def setup_directories(self):
        """设置目录结构"""
        dirs = ['mp3', 'txt', 'temp']
        created = ensure_directories(*dirs)
        if created > 0:
            logger.info(f"创建了 {created} 个目录")
    
    def setup_components(self):
        """初始化组件"""
        try:
            # 初始化下载器
            self.downloader = YouTubeDownloader()
            logger.info("YouTube下载器初始化成功")
            
            # 初始化转录器（延迟加载）
            self.transcriber = None
            logger.info("组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    def init_transcriber(self, model_size="base"):
        """初始化转录器"""
        if self.transcriber is None:
            try:
                print(f"正在初始化Whisper模型 ({model_size})...")
                self.transcriber = AudioTranscriber(model_size=model_size)
                print("转录器初始化成功")
            except Exception as e:
                logger.error(f"转录器初始化失败: {e}")
                return False
        return True
    
    def process_video(self, url, model_size="base"):
        """
        处理YouTube视频的完整流程
        
        Args:
            url: YouTube视频URL
            model_size: Whisper模型大小
            
        Returns:
            处理结果字典
        """
        result = {
            'success': False,
            'url': url,
            'video_info': None,
            'subtitle_path': None,
            'audio_path': None,
            'transcript_path': None,
            'method': None,  # 'subtitle' 或 'audio_transcription'
            'error': None
        }
        
        try:
            # 1. 验证URL
            print("正在验证URL...")
            is_valid, validated_url = validate_youtube_url(url)
            if not is_valid:
                result['error'] = f"URL验证失败: {validated_url}"
                return result
            
            result['url'] = validated_url
            
            # 2. 获取视频信息
            print("正在获取视频信息...")
            video_info = self.downloader.get_video_info(validated_url)
            if not video_info:
                result['error'] = "无法获取视频信息"
                return result
            
            result['video_info'] = video_info
            print(f"视频标题: {video_info['title']}")
            print(f"时长: {format_duration(video_info['duration'])}")
            print(f"上传者: {video_info['uploader']}")
            
            # 3. 尝试下载字幕
            print("\n正在尝试下载字幕...")
            subtitle_success, subtitle_result = self.downloader.download_subtitles(validated_url)
            
            if subtitle_success:
                # 字幕下载成功
                result['success'] = True
                result['subtitle_path'] = subtitle_result
                result['method'] = 'subtitle'
                print(f"✓ 字幕下载成功: {Path(subtitle_result).name}")
                return result
            else:
                print(f"✗ 字幕下载失败: {subtitle_result}")
            
            # 4. 下载音频
            print("\n正在下载音频...")
            audio_success, audio_result = self.downloader.download_audio(validated_url)
            
            if not audio_success:
                result['error'] = f"音频下载失败: {audio_result}"
                return result
            
            result['audio_path'] = audio_result
            print(f"✓ 音频下载成功: {Path(audio_result).name}")
            
            # 5. 转录音频
            print("\n正在转录音频...")
            if not self.init_transcriber(model_size):
                result['error'] = "转录器初始化失败"
                return result
            
            transcript_success, transcript_result = self.transcriber.transcribe_audio(
                audio_result, 
                language='zh'  # 指定中文，可以根据需要调整
            )
            
            if transcript_success:
                result['success'] = True
                result['transcript_path'] = transcript_result
                result['method'] = 'audio_transcription'
                print(f"✓ 转录完成: {Path(transcript_result).name}")
            else:
                result['error'] = f"转录失败: {transcript_result}"
            
            return result
            
        except Exception as e:
            logger.error(f"处理视频时出错: {e}")
            result['error'] = str(e)
            return result
    
    def cleanup(self):
        """清理临时文件"""
        try:
            cleaned = clean_temp_files('temp')
            if cleaned > 0:
                print(f"清理了 {cleaned} 个临时文件")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")


def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("    YouTube 音频下载和转录工具")
    print("    支持字幕下载和音频转文字")
    print("=" * 60)


def print_help():
    """打印帮助信息"""
    print("\n使用说明:")
    print("1. 输入YouTube视频URL")
    print("2. 程序会优先尝试下载字幕")
    print("3. 如果没有字幕，会下载音频并转录为文字")
    print("4. 结果保存在txt文件夹中")
    print("\n支持的URL格式:")
    print("- https://www.youtube.com/watch?v=VIDEO_ID")
    print("- https://youtu.be/VIDEO_ID")
    print("\n命令:")
    print("- 'help' 或 'h': 显示帮助")
    print("- 'info' 或 'i': 显示系统信息")
    print("- 'quit' 或 'q': 退出程序")


def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    print("正在检查系统环境...")
    sys_info = check_system_requirements()
    
    # 检查FFmpeg
    ffmpeg_ok, ffmpeg_info = sys_info['ffmpeg']
    if not ffmpeg_ok:
        print(f"⚠️  警告: {ffmpeg_info}")
        print("   音频下载功能可能无法正常工作")
        print("   请安装FFmpeg: https://ffmpeg.org/download.html")
    
    try:
        # 初始化处理器
        processor = YouTubeProcessor()
        print("✓ 系统初始化完成\n")
        
        # 显示帮助信息
        print_help()
        
        while True:
            print("\n" + "-" * 40)
            user_input = input("请输入YouTube视频URL (或输入命令): ").strip()
            
            if not user_input:
                continue
            
            # 处理命令
            if user_input.lower() in ['quit', 'q', 'exit']:
                print("正在退出...")
                processor.cleanup()
                break
            elif user_input.lower() in ['help', 'h']:
                print_help()
                continue
            elif user_input.lower() in ['info', 'i']:
                print_system_info()
                continue
            
            # 选择模型大小
            print("\n可用的Whisper模型:")
            print("1. tiny   - 最快，准确度较低")
            print("2. base   - 平衡速度和准确度 (推荐)")
            print("3. small  - 较好准确度")
            print("4. medium - 高准确度")
            print("5. large  - 最高准确度，速度较慢")
            
            model_choice = input("选择模型 (1-5) [默认: 2]: ").strip()
            model_map = {'1': 'tiny', '2': 'base', '3': 'small', '4': 'medium', '5': 'large'}
            model_size = model_map.get(model_choice, 'base')
            
            print(f"\n开始处理视频 (使用 {model_size} 模型)...")
            print("=" * 40)
            
            # 处理视频
            start_time = datetime.now()
            result = processor.process_video(user_input, model_size)
            end_time = datetime.now()
            
            # 显示结果
            print("\n" + "=" * 40)
            print("处理结果:")
            
            if result['success']:
                print("✓ 处理成功!")
                print(f"方法: {'字幕下载' if result['method'] == 'subtitle' else '音频转录'}")
                
                if result['subtitle_path']:
                    print(f"字幕文件: {result['subtitle_path']}")
                if result['audio_path']:
                    print(f"音频文件: {result['audio_path']}")
                if result['transcript_path']:
                    print(f"转录文件: {result['transcript_path']}")
                    
                processing_time = (end_time - start_time).total_seconds()
                print(f"处理时间: {format_duration(processing_time)}")
            else:
                print("✗ 处理失败!")
                print(f"错误: {result['error']}")
            
            print("=" * 40)
    
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        print(f"程序出错: {e}")
    finally:
        print("感谢使用!")


if __name__ == "__main__":
    main()
