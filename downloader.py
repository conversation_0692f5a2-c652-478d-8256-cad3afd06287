"""
YouTube视频下载模块
支持字幕下载和音频下载功能
"""

import os
import re
import yt_dlp
from pathlib import Path
from datetime import datetime
import logging
from utils import get_ffmpeg_path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class YouTubeDownloader:
    """YouTube下载器类"""
    
    def __init__(self, mp3_dir="mp3", txt_dir="txt", temp_dir="temp"):
        """
        初始化下载器
        
        Args:
            mp3_dir: 音频文件保存目录
            txt_dir: 文本文件保存目录  
            temp_dir: 临时文件目录
        """
        self.mp3_dir = Path(mp3_dir)
        self.txt_dir = Path(txt_dir)
        self.temp_dir = Path(temp_dir)
        
        # 确保目录存在
        self.mp3_dir.mkdir(exist_ok=True)
        self.txt_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
    
    def sanitize_filename(self, filename):
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    def get_video_info(self, url):
        """
        获取视频信息
        
        Args:
            url: YouTube视频URL
            
        Returns:
            视频信息字典
        """
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }
        
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'upload_date': info.get('upload_date', ''),
                    'id': info.get('id', ''),
                    'has_subtitles': bool(info.get('subtitles') or info.get('automatic_captions'))
                }
        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
            return None
    
    def download_subtitles(self, url):
        """
        下载视频字幕
        
        Args:
            url: YouTube视频URL
            
        Returns:
            tuple: (是否成功, 字幕文件路径或错误信息)
        """
        try:
            # 获取视频信息
            info = self.get_video_info(url)
            if not info:
                return False, "无法获取视频信息"
            
            if not info['has_subtitles']:
                return False, "视频没有可用的字幕"
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = self.sanitize_filename(info['title'])
            subtitle_filename = f"{safe_title}_{timestamp}.txt"
            subtitle_path = self.txt_dir / subtitle_filename
            
            # 下载字幕配置
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitleslangs': ['zh-Hans', 'zh', 'en'],  # 优先中文，备选英文
                'subtitlesformat': 'vtt',
                'outtmpl': str(self.temp_dir / '%(title)s.%(ext)s'),
                'quiet': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            # 查找下载的字幕文件
            vtt_files = list(self.temp_dir.glob("*.vtt"))
            if not vtt_files:
                return False, "字幕下载失败"
            
            # 转换VTT到纯文本
            vtt_file = vtt_files[0]
            subtitle_text = self._convert_vtt_to_text(vtt_file)
            
            # 保存纯文本字幕
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write(subtitle_text)
            
            # 清理临时文件
            vtt_file.unlink()
            
            logger.info(f"字幕下载成功: {subtitle_path}")
            return True, str(subtitle_path)
            
        except Exception as e:
            logger.error(f"字幕下载失败: {e}")
            return False, str(e)
    
    def _convert_vtt_to_text(self, vtt_file):
        """
        将VTT字幕文件转换为纯文本
        
        Args:
            vtt_file: VTT文件路径
            
        Returns:
            纯文本内容
        """
        text_lines = []
        
        with open(vtt_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            # 跳过时间戳行和空行
            if (line and 
                not line.startswith('WEBVTT') and 
                not re.match(r'\d{2}:\d{2}:\d{2}\.\d{3}', line) and
                not line.startswith('NOTE')):
                text_lines.append(line)
        
        return '\n'.join(text_lines)
    
    def download_audio(self, url):
        """
        下载视频音频

        Args:
            url: YouTube视频URL

        Returns:
            tuple: (是否成功, 音频文件路径或错误信息)
        """
        try:
            # 获取视频信息
            info = self.get_video_info(url)
            if not info:
                return False, "无法获取视频信息"

            # 获取FFmpeg路径
            ffmpeg_path = get_ffmpeg_path()
            if not ffmpeg_path:
                return False, "FFmpeg未找到，请确保ffmpeg/bin/ffmpeg.exe存在"

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = self.sanitize_filename(info['title'])
            audio_filename = f"{safe_title}_{timestamp}.mp3"
            audio_path = self.mp3_dir / audio_filename

            # 下载音频配置
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(audio_path.with_suffix('')),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'quiet': False,
                'ffmpeg_location': str(Path(ffmpeg_path).parent),  # 指定FFmpeg目录
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])

            if audio_path.exists():
                logger.info(f"音频下载成功: {audio_path}")
                return True, str(audio_path)
            else:
                return False, "音频文件未生成"

        except Exception as e:
            logger.error(f"音频下载失败: {e}")
            return False, str(e)


def main():
    """测试函数"""
    downloader = YouTubeDownloader()
    
    # 测试URL
    test_url = input("请输入YouTube视频URL: ")
    
    print("正在获取视频信息...")
    info = downloader.get_video_info(test_url)
    if info:
        print(f"视频标题: {info['title']}")
        print(f"时长: {info['duration']}秒")
        print(f"是否有字幕: {info['has_subtitles']}")
    
    print("\n尝试下载字幕...")
    success, result = downloader.download_subtitles(test_url)
    if success:
        print(f"字幕下载成功: {result}")
    else:
        print(f"字幕下载失败: {result}")
        print("\n开始下载音频...")
        success, result = downloader.download_audio(test_url)
        if success:
            print(f"音频下载成功: {result}")
        else:
            print(f"音频下载失败: {result}")


if __name__ == "__main__":
    main()
