# B站 音频下载和转录工具

这是一个功能强大的B站视频处理工具，可以自动下载字幕或音频，并将音频转录为文字。

## 功能特点

- **智能处理流程**: 优先下载字幕，如果没有字幕则下载音频并转录
- **高质量转录**: 使用OpenAI Whisper模型进行音频转文字
- **多种模型选择**: 支持tiny到large等多种Whisper模型
- **用户友好**: 简洁的命令行界面，实时进度显示
- **自动文件管理**: 自动创建目录，智能文件命名
- **错误处理**: 完善的错误处理和重试机制

## 安装要求

### 系统要求
- Python 3.7+
- FFmpeg (用于音频处理)

### Python依赖包
```bash
pip install -r requirements.txt
```

### FFmpeg安装
- **Windows**: 从 https://ffmpeg.org/download.html 下载并添加到PATH
- **macOS**: `brew install ffmpeg`
- **Linux**: `sudo apt install ffmpeg` 或 `sudo yum install ffmpeg`

## 使用方法

### 1. 运行主程序
```bash
python main.py
```

### 2. 输入B站 URL
支持以下格式的URL:
- `https://www.bilibili.com/video/BV...`
- `https://www.bilibili.com/video/av...`
- `https://b23.tv/BV...`
- `https://m.bilibili.com/video/BV...`

### 3. 选择Whisper模型
- **tiny**: 最快，准确度较低
- **base**: 平衡速度和准确度 (推荐)
- **small**: 较好准确度
- **medium**: 高准确度
- **large**: 最高准确度，速度较慢

### 4. 等待处理完成
程序会自动:
1. 验证URL
2. 获取视频信息
3. 尝试下载字幕
4. 如果没有字幕，下载音频并转录
5. 保存结果到txt文件夹

## 项目结构

```
B站文案下载/
├── main.py              # 主程序入口
├── downloader.py        # B站下载模块
├── transcriber.py       # 音频转录模块
├── utils.py            # 工具函数
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
├── mp3/                # 音频文件存储目录
├── txt/                # 转录文本存储目录
└── temp/               # 临时文件目录
```

## 输出文件格式

### 字幕文件
- 文件名: `视频标题_时间戳.txt`
- 内容: 纯文本字幕内容

### 转录文件
- 文件名: `视频标题_transcript_时间戳.txt`
- 内容包括:
  - 基本信息 (转录时间、检测语言等)
  - 完整转录文本
  - 分段转录文本 (带时间戳)

## 命令说明

在程序运行时，可以使用以下命令:
- `help` 或 `h`: 显示帮助信息
- `info` 或 `i`: 显示系统信息
- `quit` 或 `q`: 退出程序

## 故障排除

### 1. FFmpeg相关错误
- 确保FFmpeg已正确安装并添加到系统PATH
- Windows用户可能需要重启命令行窗口

### 2. 网络连接问题
- 检查网络连接
- 某些地区可能需要使用代理

### 3. 内存不足
- 对于长视频，建议使用较小的Whisper模型
- 确保有足够的磁盘空间

### 4. 模型下载缓慢
- Whisper模型首次使用时需要下载
- 可以手动下载模型文件到缓存目录

## 技术细节

### 使用的主要库
- **yt-dlp**: B站视频下载
- **openai-whisper**: 音频转文字
- **ffmpeg-python**: 音频处理
- **tqdm**: 进度条显示

### 支持的音频格式
- MP3, WAV, M4A, FLAC, OGG, WMA

### 支持的字幕语言
- 中文 (简体/繁体)
- 英文
- 其他B站支持的语言

## 许可证

本项目仅供学习和个人使用。请遵守B站的服务条款和相关法律法规。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持字幕下载和音频转录
- 集成Whisper模型
- 完整的用户界面

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
