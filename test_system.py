"""
系统测试脚本
验证所有模块是否正常工作
"""

import sys
from pathlib import Path

def test_imports():
    """测试模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    modules = [
        ('utils', '工具函数模块'),
        ('downloader', 'B站下载模块'),
        ('transcriber', '音频转录模块'),
        ('yt_dlp', 'yt-dlp库'),
        ('whisper', 'OpenAI Whisper库'),
        ('torch', 'PyTorch库'),
    ]
    
    success_count = 0
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"✅ {description}: 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description}: 导入失败 - {e}")
        except Exception as e:
            print(f"⚠️  {description}: 导入异常 - {e}")
    
    print(f"\n导入测试结果: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)


def test_system_environment():
    """测试系统环境"""
    print("\n" + "=" * 50)
    print("测试系统环境")
    print("=" * 50)
    
    try:
        from utils import check_system_requirements, print_system_info
        print_system_info()
        return True
    except Exception as e:
        print(f"❌ 系统环境检查失败: {e}")
        return False


def test_directories():
    """测试目录结构"""
    print("\n" + "=" * 50)
    print("测试目录结构")
    print("=" * 50)
    
    required_dirs = ['mp3', 'txt', 'temp', 'ffmpeg/bin']
    all_good = True
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"✅ {dir_name}/: 存在")
        else:
            print(f"❌ {dir_name}/: 不存在")
            all_good = False
    
    return all_good


def test_ffmpeg():
    """测试FFmpeg"""
    print("\n" + "=" * 50)
    print("测试FFmpeg")
    print("=" * 50)
    
    try:
        from utils import check_ffmpeg, get_ffmpeg_path
        
        ffmpeg_path = get_ffmpeg_path()
        if ffmpeg_path:
            print(f"✅ FFmpeg路径: {ffmpeg_path}")
        else:
            print("❌ FFmpeg路径未找到")
            return False
        
        is_ok, info, path = check_ffmpeg()
        if is_ok:
            print(f"✅ FFmpeg版本: {info}")
            return True
        else:
            print(f"❌ FFmpeg检查失败: {info}")
            return False
            
    except Exception as e:
        print(f"❌ FFmpeg测试异常: {e}")
        return False


def test_url_validation():
    """测试URL验证"""
    print("\n" + "=" * 50)
    print("测试URL验证")
    print("=" * 50)
    
    try:
        from utils import validate_bilibili_url

        test_urls = [
            ("https://www.bilibili.com/video/BV1xx411c7mD", True),
            ("https://www.bilibili.com/video/av12345", True),
            ("https://b23.tv/BV1xx411c7mD", True),
            ("invalid_url", False),
            ("", False),
        ]
        
        all_passed = True
        for url, expected in test_urls:
            is_valid, result = validate_bilibili_url(url)
            status = "✅" if is_valid == expected else "❌"
            print(f"{status} {url}: {'有效' if is_valid else '无效'}")
            if is_valid != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ URL验证测试异常: {e}")
        return False


def test_whisper_models():
    """测试Whisper模型"""
    print("\n" + "=" * 50)
    print("测试Whisper模型")
    print("=" * 50)
    
    try:
        import whisper
        models = whisper.available_models()
        print(f"✅ 可用的Whisper模型: {', '.join(models)}")
        
        # 测试加载最小的模型
        print("正在测试加载tiny模型...")
        model = whisper.load_model("tiny")
        print("✅ tiny模型加载成功")
        return True
        
    except Exception as e:
        print(f"❌ Whisper模型测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("B站音频下载和转录工具 - 系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("系统环境", test_system_environment),
        ("目录结构", test_directories),
        ("FFmpeg", test_ffmpeg),
        ("URL验证", test_url_validation),
        ("Whisper模型", test_whisper_models),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统准备就绪。")
        print("现在可以运行: python main.py")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
    
    print("=" * 60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
